package com.suncent.smc.persistence.promotion.mapper;

import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * BD促销记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface AmBestDealRecordMapper {
    /**
     * 查询BD促销记录
     * 
     * @param id BD促销记录主键
     * @return BD促销记录
     */
    public AmBestDealRecord selectAmBestDealRecordById(Long id);

    /**
     * 查询BD促销记录列表
     *
     * @param amBestDealRecord BD促销记录
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordList(AmBestDealRecord amBestDealRecord);

    /**
     * 查询BD促销记录列表（包含ASIN数量）
     *
     * @param amBestDealRecord BD促销记录
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordListWithAsinCount(AmBestDealRecord amBestDealRecord);

    /**
     * 新增BD促销记录
     * 
     * @param amBestDealRecord BD促销记录
     * @return 结果
     */
    public int insertAmBestDealRecord(AmBestDealRecord amBestDealRecord);

    /**
     * 修改BD促销记录
     * 
     * @param amBestDealRecord BD促销记录
     * @return 结果
     */
    public int updateAmBestDealRecord(AmBestDealRecord amBestDealRecord);

    /**
     * 删除BD促销记录
     * 
     * @param id BD促销记录主键
     * @return 结果
     */
    public int deleteAmBestDealRecordById(Long id);

    /**
     * 批量删除BD促销记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAmBestDealRecordByIds(String[] ids);

    /**
     * 根据促销ID查询BD记录
     *
     * @param promotionId 促销ID
     * @return BD促销记录
     */
    public AmBestDealRecord selectAmBestDealRecordByPromotionId(String promotionId);

    /**
     * 根据促销ID列表批量查询BD记录
     *
     * @param promotionIds 促销ID列表
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordByPromotionIds(List<String> promotionIds);

    /**
     * 根据ID列表批量查询BD记录
     *
     * @param ids ID列表
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordByIds(List<Long> ids);

    /**
     * 根据站点和状态查询BD记录列表
     *
     * @param site 站点
     * @param status 状态
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordBySiteAndStatus(String site, String status);

    /**
     * 按站点统计BD促销记录
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> getStatsBySite();

    /**
     * 按状态统计BD促销记录
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> getStatsByStatus();

    /**
     * 按刊登类型统计BD促销记录
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> getStatsByPublishType();

    /**
     * 获取BD促销记录总数
     *
     * @return 总数
     */
    public int getTotalCount();

    /**
     * 获取最近N天创建的记录数量
     *
     * @param days 天数
     * @return 数量
     */
    public int getRecentCreatedCount(int days);

    /**
     * 获取总ASIN数量
     *
     * @return 总ASIN数量
     */
    public int getTotalAsinCount();

    /**
     * 根据状态获取记录数量
     *
     * @param status 状态
     * @return 数量
     */
    public int getCountByStatus(String status);

    /**
     * 获取创建趋势（最近N天）
     *
     * @param days 天数
     * @return 创建趋势数据
     */
    public List<Map<String, Object>> getCreateTrend(int days);

    /**
     * 获取所有不重复的状态值
     *
     * @return 状态列表
     */
    public List<String> selectDistinctStatusList();
}
