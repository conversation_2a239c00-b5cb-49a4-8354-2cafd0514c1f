package com.suncent.smc.persistence.publication.service;

import com.suncent.smc.persistence.publication.domain.entity.ListingAmazonAttributeLineV2;

import java.util.List;

/**
 * 亚马逊商品属性信息行Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface IListingAmazonAttributeLineV2Service 
{
    /**
     * 查询亚马逊商品属性信息行
     * 
     * @param id 亚马逊商品属性信息行主键
     * @return 亚马逊商品属性信息行
     */
    public ListingAmazonAttributeLineV2 selectListingAmazonAttributeLineV2ById(Long id);

    /**
     * 查询亚马逊商品属性信息行列表
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 亚马逊商品属性信息行集合
     */
    public List<ListingAmazonAttributeLineV2> selectListingAmazonAttributeLineV2List(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2);

    /**
     * 新增亚马逊商品属性信息行
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 结果
     */
    public int insertListingAmazonAttributeLineV2(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2);

    /**
     * 修改亚马逊商品属性信息行
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 结果
     */
    public int updateListingAmazonAttributeLineV2(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2);

    /**
     * 批量删除亚马逊商品属性信息行
     * 
     * @param ids 需要删除的亚马逊商品属性信息行主键集合
     * @return 结果
     */
    public int deleteListingAmazonAttributeLineV2ByIds(String ids);

    /**
     * 删除亚马逊商品属性信息行信息
     * 
     * @param id 亚马逊商品属性信息行主键
     * @return 结果
     */
    public int deleteListingAmazonAttributeLineV2ById(Long id);

    /**
     * 根据商品ID查询亚马逊商品属性信息行列表
     * 分组需要加入tableType，数据key会重复
     * @param headId 商品ID
     * @return 亚马逊商品属性信息行集合
     */
    List<ListingAmazonAttributeLineV2> listByGoodsId(Integer headId);

    void deleteByGoodsId(Integer goodsHeadId);

    List<ListingAmazonAttributeLineV2> selectExtendAttrByGoodsId(Integer goodsId);

    List<ListingAmazonAttributeLineV2> selecAttrByGoodsIds(List<Long> goodsId);

    String getPn(Integer goodsId);

    void setPn(Integer goodsId,String newPn);

    ListingAmazonAttributeLineV2 getPnObj(Integer goodsId);

    ListingAmazonAttributeLineV2 getAttrByPropNodePath(Integer id, String propNodePath);

    void deleteListingAmazonAttributeLineV2ByGoodId(Integer goodsId, List<String> propNodePaths, List<Integer> tableTypes);

    void updateCategoryIdByGoodsId(Integer goodsId, Integer categoryId);

    String getFollowAsin(Integer id);


    List<ListingAmazonAttributeLineV2> getAttrByPropNodePathAndHeadIds(List<Integer> goodsId, String propNodePath);


    void deleteGTINByGoodId(Integer goodsId);

    public void deleteListingAmazonAttributeLineV2ByGoodIdAndPathList(Integer goodsId, List<String> deleteAttrNodePathList);


    String getValueByPropNodePath(Integer id, String propNodePath);
}
