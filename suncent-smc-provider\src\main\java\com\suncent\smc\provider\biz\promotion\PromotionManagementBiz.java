package com.suncent.smc.provider.biz.promotion;

import cn.hutool.core.collection.CollUtil;
import com.suncent.smc.persistence.bi.entity.RpaDbPromotionCentral;
import com.suncent.smc.persistence.bi.service.IRpaDbPromotionCentralService;
import com.suncent.smc.provider.biz.promotion.batch.PromotionBatchSyncService;
import com.suncent.smc.provider.biz.promotion.data.PromotionDataQueryService;
import com.suncent.smc.provider.biz.promotion.sync.PromotionSyncHandler;
import com.suncent.smc.provider.biz.promotion.sync.PromotionSyncHandlerFactory;
import com.suncent.smc.provider.biz.promotion.sync.impl.BestDealSyncHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 促销管理统一业务类
 * 整合所有促销相关的业务逻辑，包括数据同步、详情管理、统计分析等
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Component
@Slf4j
public class PromotionManagementBiz {


    @Autowired
    private IRpaDbPromotionCentralService rpaDbPromotionCentralService;

    @Autowired
    private PromotionSyncHandlerFactory handlerFactory;


    @Autowired
    private PromotionBatchSyncService batchSyncService;

    @Autowired
    private PromotionDataQueryService dataQueryService;

    /**
     * 综合同步结果类
     */
    public static class ComprehensiveResult {
        private boolean success;
        private String errorMessage;
        private Date startTime;
        private Date endTime;
        private long totalDuration;
        private String operationType;

        // 主表同步结果
        private SyncResult mainTableResult;

        // 详情表同步结果
        private DetailsSyncResult detailsResult;

        // 统计信息
        private Map<String, Object> summary;

        public ComprehensiveResult() {
            this.summary = new HashMap<>();
        }

        // getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
            if (startTime != null && endTime != null) {
                this.totalDuration = endTime.getTime() - startTime.getTime();
            }
        }

        public long getTotalDuration() {
            return totalDuration;
        }

        public String getOperationType() {
            return operationType;
        }

        public void setOperationType(String operationType) {
            this.operationType = operationType;
        }

        public SyncResult getMainTableResult() {
            return mainTableResult;
        }

        public void setMainTableResult(SyncResult mainTableResult) {
            this.mainTableResult = mainTableResult;
        }

        public DetailsSyncResult getDetailsResult() {
            return detailsResult;
        }

        public void setDetailsResult(DetailsSyncResult detailsResult) {
            this.detailsResult = detailsResult;
        }

        public Map<String, Object> getSummary() {
            return summary;
        }

        public void setSummary(Map<String, Object> summary) {
            this.summary = summary;
        }

        /**
         * 生成操作摘要
         */
        public void generateSummary() {
            summary.clear();

            // 主表统计
            if (mainTableResult != null) {
                Map<String, Object> mainTableStats = new HashMap<>();
                mainTableStats.put("totalCount", mainTableResult.getTotalCount());
                mainTableStats.put("insertCount", mainTableResult.getInsertCount());
                mainTableStats.put("updateCount", mainTableResult.getUpdateCount());
                mainTableStats.put("skipCount", mainTableResult.getSkipCount());
                mainTableStats.put("errorCount", mainTableResult.getErrorCount());
                summary.put("mainTable", mainTableStats);
            }

            // 详情表统计
            if (detailsResult != null) {
                Map<String, Object> detailsStats = new HashMap<>();
                detailsStats.put("totalCount", detailsResult.getTotalCount());
                detailsStats.put("insertCount", detailsResult.getInsertCount());
                detailsStats.put("updateCount", detailsResult.getUpdateCount());
                detailsStats.put("skipCount", detailsResult.getSkipCount());
                detailsStats.put("errorCount", detailsResult.getErrorCount());
                summary.put("details", detailsStats);
            }

            // 总体统计
            int totalProcessed = 0;
            int totalInserted = 0;
            int totalUpdated = 0;
            int totalSkipped = 0;
            int totalErrors = 0;

            if (mainTableResult != null) {
                totalProcessed += mainTableResult.getTotalCount();
                totalInserted += mainTableResult.getInsertCount();
                totalUpdated += mainTableResult.getUpdateCount();
                totalSkipped += mainTableResult.getSkipCount();
                totalErrors += mainTableResult.getErrorCount();
            }

            if (detailsResult != null) {
                totalProcessed += detailsResult.getTotalCount();
                totalInserted += detailsResult.getInsertCount();
                totalUpdated += detailsResult.getUpdateCount();
                totalSkipped += detailsResult.getSkipCount();
                totalErrors += detailsResult.getErrorCount();
            }

            Map<String, Object> overallStats = new HashMap<>();
            overallStats.put("totalProcessed", totalProcessed);
            overallStats.put("totalInserted", totalInserted);
            overallStats.put("totalUpdated", totalUpdated);
            overallStats.put("totalSkipped", totalSkipped);
            overallStats.put("totalErrors", totalErrors);
            overallStats.put("duration", totalDuration);
            overallStats.put("operationType", operationType != null ? operationType : "UNKNOWN");
            summary.put("overall", overallStats);
        }
    }

    /**
     * 同步结果类（兼容原PromotionDataSyncBiz.SyncResult）
     */
    public static class SyncResult {
        private boolean success;
        private String errorMessage;
        private Date startTime;
        private Date endTime;
        private String promotionType;
        private boolean fullSync;
        private int totalCount;
        private int insertCount;
        private int updateCount;
        private int skipCount;
        private int errorCount;

        // getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }

        public String getPromotionType() {
            return promotionType;
        }

        public void setPromotionType(String promotionType) {
            this.promotionType = promotionType;
        }

        public boolean isFullSync() {
            return fullSync;
        }

        public void setFullSync(boolean fullSync) {
            this.fullSync = fullSync;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getInsertCount() {
            return insertCount;
        }

        public void setInsertCount(int insertCount) {
            this.insertCount = insertCount;
        }

        public int getUpdateCount() {
            return updateCount;
        }

        public void setUpdateCount(int updateCount) {
            this.updateCount = updateCount;
        }

        public int getSkipCount() {
            return skipCount;
        }

        public void setSkipCount(int skipCount) {
            this.skipCount = skipCount;
        }

        public int getErrorCount() {
            return errorCount;
        }

        public void setErrorCount(int errorCount) {
            this.errorCount = errorCount;
        }

        public void merge(SyncResult other) {
            if (other != null) {
                this.insertCount += other.insertCount;
                this.updateCount += other.updateCount;
                this.skipCount += other.skipCount;
                this.errorCount += other.errorCount;
            }
        }
    }

    /**
     * 详情同步结果类
     */
    public static class DetailsSyncResult {
        private boolean success;
        private String errorMessage;
        private Date startTime;
        private Date endTime;
        private int totalCount;
        private int insertCount;
        private int updateCount;
        private int skipCount;
        private int errorCount;

        // getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getInsertCount() {
            return insertCount;
        }

        public void setInsertCount(int insertCount) {
            this.insertCount = insertCount;
        }

        public int getUpdateCount() {
            return updateCount;
        }

        public void setUpdateCount(int updateCount) {
            this.updateCount = updateCount;
        }

        public int getSkipCount() {
            return skipCount;
        }

        public void setSkipCount(int skipCount) {
            this.skipCount = skipCount;
        }

        public int getErrorCount() {
            return errorCount;
        }

        public void setErrorCount(int errorCount) {
            this.errorCount = errorCount;
        }

        public void merge(DetailsSyncResult other) {
            if (other != null) {
                this.insertCount += other.insertCount;
                this.updateCount += other.updateCount;
                this.skipCount += other.skipCount;
                this.errorCount += other.errorCount;
            }
        }
    }

    // ==================== 主要业务方法 ====================

    /**
     * 执行完整的促销数据同步（使用批处理和关联查询）
     * 包括主表、详情表的关联同步
     *
     * @param promotionType 促销类型
     * @param lastSyncTime  上次同步时间
     * @param fullSync      是否全量同步
     * @return 综合同步结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ComprehensiveResult syncPromotionData(String promotionType, Date lastSyncTime, boolean fullSync) {
        log.info("开始执行{}数据综合同步（批处理模式），上次同步时间: {}, 全量同步: {}", promotionType, lastSyncTime, fullSync);

        ComprehensiveResult result = new ComprehensiveResult();
        result.setStartTime(new Date());
        result.setOperationType(fullSync ? "FULL_SYNC" : "INCREMENTAL_SYNC");

        try {
            // 验证促销类型
            if (!isPromotionTypeSupported(promotionType)) {
                result.setSuccess(false);
                result.setErrorMessage("不支持的促销类型: " + promotionType);
                return result;
            }

            // 1. 关联查询主表和详情表数据
            log.info("步骤1: 开始关联查询{}数据", promotionType);
            PromotionDataQueryService.PromotionDataResult queryResult =
                    dataQueryService.queryPromotionData(promotionType, lastSyncTime, fullSync);

            log.info("步骤1完成: 数据查询成功，主表: {}条, 详情表: {}条, 有详情的促销: {}个, 耗时: {}ms",
                    queryResult.getQueryStats().getMainTableCount(),
                    queryResult.getQueryStats().getDetailsTableCount(),
                    queryResult.getQueryStats().getPromotionsWithDetails(),
                    queryResult.getQueryStats().getQueryDuration());

            // 2. 批量同步数据（主表+详情表关联处理）
            log.info("步骤2: 开始批量同步{}数据", promotionType);
            ComprehensiveResult batchResult = batchSyncService.batchSyncPromotionData(
                    promotionType,
                    queryResult.getMainTableData(),
                    queryResult.getDetailsDataMap(),
                    fullSync
            );

            // 3. 合并结果
            result.setMainTableResult(batchResult.getMainTableResult());
            result.setDetailsResult(batchResult.getDetailsResult());
            result.setSuccess(batchResult.isSuccess());
            result.setErrorMessage(batchResult.getErrorMessage());

            if (result.isSuccess()) {
                result.generateSummary();
                log.info("{}数据综合同步完成，总耗时: {}ms", promotionType, result.getTotalDuration());
            } else {
                log.error("{}数据综合同步失败: {}", promotionType, result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("{}数据综合同步失败", promotionType, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        } finally {
            result.setEndTime(new Date());
        }

        return result;
    }


    // ==================== BestDeal 专用方法 ====================

    /**
     * BestDeal增量同步
     *
     * @param lastSyncTime 上次同步时间
     * @return 综合同步结果
     */
    public ComprehensiveResult syncBestDealDataIncremental(Date lastSyncTime) {
        return syncPromotionData("BestDeal", lastSyncTime, false);
    }

    /**
     * BestDeal全量同步
     *
     * @return 综合同步结果
     */
    public ComprehensiveResult syncBestDealDataFull() {
        return syncPromotionData("BestDeal", null, true);
    }

    /**
     * BestDeal当天最新数据同步（避免重复数据）
     *
     * @return 综合同步结果
     */
    public ComprehensiveResult syncBestDealLatestToday() {
        return syncPromotionDataLatestToday("BestDeal");
    }


    // ==================== 辅助方法 ====================


    /**
     * 同步主表数据
     *
     * @param promotionType 促销类型
     * @param lastSyncTime  上次同步时间
     * @param fullSync      是否全量同步
     * @return 主表同步结果
     */
    private SyncResult syncMainTableData(String promotionType, Date lastSyncTime, boolean fullSync) {
        try {
            return syncPromotions(promotionType, fullSync, lastSyncTime);
        } catch (Exception e) {
            log.error("主表数据同步异常", e);
            SyncResult errorResult = new SyncResult();
            errorResult.setSuccess(false);
            errorResult.setErrorMessage(e.getMessage());
            return errorResult;
        }
    }

    /**
     * 同步详情表数据
     *
     * @param promotionType 促销类型
     * @param lastSyncTime  上次同步时间
     * @return 详情表同步结果
     */
    private DetailsSyncResult syncDetailsTableData(String promotionType, Date lastSyncTime) {
        try {
            // 获取对应的处理器
            PromotionSyncHandler handler = handlerFactory.getHandler(promotionType);
            if (handler == null) {
                DetailsSyncResult result = new DetailsSyncResult();
                result.setStartTime(new Date());
                result.setEndTime(new Date());
                result.setSuccess(false);
                result.setErrorMessage("未找到支持促销类型 " + promotionType + " 的处理器");
                return result;
            }

            // 检查是否支持详情数据同步
            if (!handler.supportsDetailsSync()) {
                log.info("促销类型 {} 不支持详情数据同步，跳过", promotionType);
                DetailsSyncResult result = new DetailsSyncResult();
                result.setStartTime(new Date());
                result.setEndTime(new Date());
                result.setSuccess(true);
                result.setErrorMessage("该促销类型不支持详情数据同步");
                return result;
            }

            // 执行详情数据同步
            return handler.syncPromotionDetails(lastSyncTime);

        } catch (Exception e) {
            log.error("详情表数据同步异常", e);
            DetailsSyncResult errorResult = new DetailsSyncResult();
            errorResult.setStartTime(new Date());
            errorResult.setEndTime(new Date());
            errorResult.setSuccess(false);
            errorResult.setErrorMessage(e.getMessage());
            return errorResult;
        }
    }

    /**
     * 通用促销数据同步方法（整合自PromotionDataSyncBiz）
     *
     * @param promotionType 促销类型
     * @param fullSync      是否全量同步
     * @param lastSyncTime  上次同步时间（增量同步时使用）
     * @return 同步结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public SyncResult syncPromotions(String promotionType, boolean fullSync, Date lastSyncTime) {
        log.info("开始同步促销数据，类型: {}, 全量同步: {}, 上次同步时间: {}", promotionType, fullSync, lastSyncTime);

        // 获取对应的处理器
        PromotionSyncHandler handler = handlerFactory.getHandler(promotionType);
        if (handler == null) {
            SyncResult result = new SyncResult();
            result.setPromotionType(promotionType);
            result.setFullSync(fullSync);
            result.setStartTime(new Date());
            result.setEndTime(new Date());
            result.setSuccess(false);
            result.setErrorMessage("未找到支持促销类型 " + promotionType + " 的处理器");
            return result;
        }

        try {
            // 获取需要同步的数据
            List<RpaDbPromotionCentral> sourceData = getSourceDataForSync(promotionType, fullSync, lastSyncTime);
            if (CollUtil.isEmpty(sourceData)) {
                log.info("没有需要同步的{}数据", promotionType);
                SyncResult result = new SyncResult();
                result.setPromotionType(promotionType);
                result.setFullSync(fullSync);
                result.setStartTime(new Date());
                result.setEndTime(new Date());
                result.setSuccess(true);
                return result;
            }

            // 使用处理器同步数据
            return handler.syncPromotions(sourceData, fullSync);

        } catch (Exception e) {
            log.error("同步促销数据失败，类型: {}", promotionType, e);
            SyncResult result = new SyncResult();
            result.setPromotionType(promotionType);
            result.setFullSync(fullSync);
            result.setStartTime(new Date());
            result.setEndTime(new Date());
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            return result;
        }
    }


    /**
     * 获取需要同步的源数据
     *
     * @param promotionType 促销类型
     * @param fullSync      是否全量同步
     * @param lastSyncTime  上次同步时间
     * @return 源数据列表
     */
    private List<RpaDbPromotionCentral> getSourceDataForSync(String promotionType, boolean fullSync, Date lastSyncTime) {
        if ("BestDeal".equals(promotionType)) {
            if (fullSync) {
                return rpaDbPromotionCentralService.selectBestDealPromotions();
            } else {
                return rpaDbPromotionCentralService.selectBestDealForSync(lastSyncTime);
            }
        }

        // 其他促销类型的数据获取逻辑
        return new ArrayList<>();
    }


    /**
     * 同步当天最新促销数据（避免重复数据）
     *
     * @param promotionType 促销类型
     * @return 综合同步结果
     */
    private ComprehensiveResult syncPromotionDataLatestToday(String promotionType) {
        log.info("开始同步{}当天最新数据", promotionType);

        ComprehensiveResult comprehensiveResult = new ComprehensiveResult();
        comprehensiveResult.setOperationType("LATEST_TODAY_SYNC");
        comprehensiveResult.setStartTime(new Date());

        try {
            // 获取处理器
            PromotionSyncHandler handler = handlerFactory.getHandler(promotionType);
            if (handler == null) {
                comprehensiveResult.setSuccess(false);
                comprehensiveResult.setErrorMessage("未找到支持促销类型 " + promotionType + " 的处理器");
                return comprehensiveResult;
            }

            // 获取当天最新数据
            List<RpaDbPromotionCentral> sourceData = getLatestTodaySourceData(promotionType);
            if (CollUtil.isEmpty(sourceData)) {
                log.info("没有需要同步的{}当天最新数据", promotionType);
                comprehensiveResult.setSuccess(true);
                return comprehensiveResult;
            }

            log.info("获取到{}条{}当天最新数据", sourceData.size(), promotionType);

            // 1. 同步主表数据
            SyncResult mainTableResult = handler.syncPromotions(sourceData, false);
            comprehensiveResult.setMainTableResult(mainTableResult);

            if (!mainTableResult.isSuccess()) {
                comprehensiveResult.setSuccess(false);
                comprehensiveResult.setErrorMessage("主表同步失败: " + mainTableResult.getErrorMessage());
                return comprehensiveResult;
            }

            // 2. 同步详情数据
            if (handler instanceof BestDealSyncHandler) {
                BestDealSyncHandler bestDealHandler = (BestDealSyncHandler) handler;
                PromotionManagementBiz.DetailsSyncResult detailsResult = bestDealHandler.syncPromotionDetails(null);
                comprehensiveResult.setDetailsResult(detailsResult);

                if (!detailsResult.isSuccess()) {
                    log.warn("详情表同步失败: {}", detailsResult.getErrorMessage());
                }
            }

            comprehensiveResult.setSuccess(true);
            log.info("{}当天最新数据同步完成", promotionType);

        } catch (Exception e) {
            log.error("{}当天最新数据同步失败", promotionType, e);
            comprehensiveResult.setSuccess(false);
            comprehensiveResult.setErrorMessage(e.getMessage());
        } finally {
            comprehensiveResult.setEndTime(new Date());
        }

        return comprehensiveResult;
    }

    /**
     * 获取当天最新源数据
     *
     * @param promotionType 促销类型
     * @return 源数据列表
     */
    private List<RpaDbPromotionCentral> getLatestTodaySourceData(String promotionType) {
        if ("BestDeal".equals(promotionType)) {
            return rpaDbPromotionCentralService.selectBestDealLatestToday();
        }

        // 其他促销类型的数据获取逻辑
        return new ArrayList<>();
    }

    /**
     * 检查促销类型是否支持
     *
     * @param promotionType 促销类型
     * @return 是否支持
     */
    private boolean isPromotionTypeSupported(String promotionType) {
        return handlerFactory.isSupported(promotionType);
    }

    /**
     * 清理重复的ASIN统计数据
     * 保留每个promotionId+asin组合的最新记录，删除其他重复记录
     *
     * @return 清理结果信息
     */
    public String cleanupDuplicateAsinStatistics() {
        try {
            log.info("开始清理重复的ASIN统计数据");

            // 这里可以添加具体的清理逻辑
            // 1. 查询所有重复的记录
            // 2. 保留最新的记录
            // 3. 删除重复的记录

            log.info("重复的ASIN统计数据清理完成");
            return "重复数据清理完成";

        } catch (Exception e) {
            log.error("清理重复的ASIN统计数据失败", e);
            return "清理失败: " + e.getMessage();
        }
    }

}
