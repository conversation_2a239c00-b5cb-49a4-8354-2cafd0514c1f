package com.suncent.smc.provider.biz.publication;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsHeadV2;
import com.suncent.smc.persistence.ebay.service.IEbayListingGoodsHeadV2Service;
import com.suncent.smc.persistence.pdm.domain.dto.*;
import com.suncent.smc.persistence.pdm.domain.dto.goodsInfoDto.GoodsInfoDTO;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.publication.domain.dto.GoodsDetailDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.temu.domain.entity.TemuGoodsHead;
import com.suncent.smc.persistence.temu.service.ITemuGoodsHeadService;
import com.suncent.smc.provider.biz.publication.domain.GoodsCategoryAttributeVO;
import com.suncent.smc.provider.biz.publication.domain.GoodsNewAttributeVO;
import com.suncent.smc.provider.biz.publication.dto.ForecastResponse;
import com.suncent.smc.provider.biz.publication.dto.PriceCountResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/11/01 11:22
 */
@Slf4j
@Component
public class BIHttpRequestBiz {
    @Value("${bi.rebate_sku_url}")
    private String biRebateSkuUrl;


    private static final String APPCODE = "smc920a45cbb14a9bibf869f57a21fde";

    @Autowired
    private RestTemplate restTemplate;
    @Value("${bi.daily-sales-forecast-url}")
    private String dailySalesForecastUrl;

    /**
     * 获取bi返利商品sku集合 分页获取
     *
     * @return
     */
    public List<String> getAllBiRebateSkus() {
        List<String> lists = new ArrayList<>();
        String pageBiRebate = sendBiRebateSkus(1, 1000);
        JSONObject jsonObject = JSONObject.parseObject(pageBiRebate);
        JSONObject data = jsonObject.getJSONObject("data");
        if (data == null) {
            return lists;
        }
        if (data.getInteger("totalNum") == null) {
            return lists;
        }
        Integer totalNum = data.getInteger("totalNum");
        Integer pageSize = data.getInteger("pageSize");
        JSONArray list = data.getJSONArray("rows");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject1 = list.getJSONObject(i);
            String sku =jsonObject1.getString("sku");
            lists.add(sku);
        }

        //计算总页数
        Integer totalPage = totalNum % pageSize == 0 ? totalNum / pageSize : totalNum / pageSize + 1;
        for (int i = 2; i <= totalPage; i++) {
            String s = sendBiRebateSkus(i, pageSize);
            jsonObject = JSONObject.parseObject(s);
            data = jsonObject.getJSONObject("data");
            if (ObjUtil.isEmpty(data)){
                continue;
            }
            list = data.getJSONArray("rows");
            for (int j = 0; j < list.size(); j++) {
                JSONObject jsonObject2 = list.getJSONObject(j);
                String sku =jsonObject2.getString("sku");
                lists.add(sku);
            }
        }

        return lists;
    }


    public String sendBiRebateSkus(Integer pageNum, Integer pageSize) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("appCode", "f0920a45cbb14a89b959869f57a21fde");
        paramMap.put("returnTotalNum", "true");
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);
        paramMap.put("sku", null);
        paramMap.put("warehouse_name", null);
        paramMap.put("rebate_object", null);
        paramMap.put("start_date", null);
        paramMap.put("end_date", null);
        //将上诉参数优雅的放到url后 进行get请求 返回结果
        String result = HttpUtils.get(biRebateSkuUrl, paramMap);
        return result;
    }


    public ForecastResponse fetchForecast(int pageNum, int pageSize) {
        String url = String.format("%s?appCode=%s&returnTotalNum=true&pageNum=%d&pageSize=%d",
                dailySalesForecastUrl, APPCODE, pageNum, pageSize);
        try {
            ResponseEntity<ForecastResponse> response = restTemplate.getForEntity(url, ForecastResponse.class);
            return response.getBody();
        } catch (Exception e) {
            log.error("调用日销接口失败", e);
            return null;
        }

    }

}
