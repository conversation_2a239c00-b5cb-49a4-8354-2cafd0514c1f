<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.publication.mapper.ListingAmazonAttributeLineV2Mapper">
    
    <resultMap type="ListingAmazonAttributeLineV2" id="ListingAmazonAttributeLineV2Result">
        <result property="id"    column="id"    />
        <result property="headId"    column="head_id"    />
        <result property="pdmGoodsCode"    column="pdm_goods_code"    />
        <result property="productType"    column="product_type"    />
        <result property="categoryId"    column="category_id"    />
        <result property="propNodePath"    column="prop_node_path"    />
        <result property="tableValue"    column="table_value"    />
        <result property="tableName"    column="table_name"    />
        <result property="tableType"    column="table_type"    />
        <result property="propertyGroupId"    column="property_group_id"    />
        <result property="vcFlag" column="vc_flag"/>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectListingAmazonAttributeLineV2Vo">
        select id,
               head_id,
               pdm_goods_code,
               product_type,
               category_id,
               prop_node_path,
               table_value,
               table_name,
               table_type,
               property_group_id,
               vc_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from sc_smc_listing_amazon_attribute_line_v2
    </sql>

    <select id="selectListingAmazonAttributeLineV2List" parameterType="ListingAmazonAttributeLineV2" resultMap="ListingAmazonAttributeLineV2Result">
        <include refid="selectListingAmazonAttributeLineV2Vo"/>
        <where>  
            <if test="headId != null "> and head_id = #{headId}</if>
            <if test="pdmGoodsCode != null  and pdmGoodsCode != ''"> and pdm_goods_code = #{pdmGoodsCode}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="propNodePath != null  and propNodePath != ''"> and prop_node_path = #{propNodePath}</if>
            <if test="tableValue != null  and tableValue != ''"> and table_value = #{tableValue}</if>
            <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="tableType != null "> and table_type = #{tableType}</if>
            <if test="propertyGroupId != null  and propertyGroupId != ''"> and property_group_id = #{propertyGroupId}</if>
            <if test="vcFlag != null ">and vc_flag = #{vcFlag}</if>
        </where>
    </select>
    
    <select id="selectListingAmazonAttributeLineV2ById" parameterType="Long" resultMap="ListingAmazonAttributeLineV2Result">
        <include refid="selectListingAmazonAttributeLineV2Vo"/>
        where id = #{id}
    </select>
    <select id="listByGoodsId" resultMap="ListingAmazonAttributeLineV2Result">
        <include refid="selectListingAmazonAttributeLineV2Vo"/>
        where head_id = #{headId}
    </select>
    <select id="selectExtendAttrByGoodsId" resultMap="ListingAmazonAttributeLineV2Result">
        <include refid="selectListingAmazonAttributeLineV2Vo"/>
        WHERE head_id = #{goodsId} AND table_type in (4,5)
    </select>

    <select id="selecAttrByGoodsIds" resultMap="ListingAmazonAttributeLineV2Result">
        <include refid="selectListingAmazonAttributeLineV2Vo"/>
        WHERE head_id in
        <foreach collection="goodsIds" open="(" separator="," close=")" item="goodsId">
            #{goodsId}
        </foreach>
    </select>

    <select id="getPn" resultType="java.lang.String">
        select table_value from sc_smc_listing_amazon_attribute_line_v2 where head_id = #{goodsId} and prop_node_path='part_number.value' limit 1
    </select>
    <select id="getPnObj" resultMap="ListingAmazonAttributeLineV2Result">
        <include refid="selectListingAmazonAttributeLineV2Vo"/>
        WHERE head_id = #{goodsId} AND prop_node_path='part_number.value' limit 1
    </select>
    <select id="getAttrByPropNodePath" resultMap="ListingAmazonAttributeLineV2Result">
        <include refid="selectListingAmazonAttributeLineV2Vo"/>
        WHERE head_id = #{goodsId} AND prop_node_path = #{propNodePath}  limit 1
    </select>
    <select id="getFollowAsin" resultType="java.lang.String">
        select table_value
        from sc_smc_listing_amazon_attribute_line_v2
        where head_id = #{goodsId}
          and table_type = 5
          and prop_node_path in ('externally_assigned_product_identifier.value', 'external_product_id.value') limit 1
    </select>
    <select id="getAttrByPropNodePathAndHeadIds" resultMap="ListingAmazonAttributeLineV2Result">
        <include refid="selectListingAmazonAttributeLineV2Vo"/>
        WHERE head_id in
        <foreach collection="goodsIds" open="(" separator="," close=")" item="goodsId">
            #{goodsId}
        </foreach>
        AND prop_node_path = #{propNodePath}
    </select>

    <insert id="insertListingAmazonAttributeLineV2" parameterType="ListingAmazonAttributeLineV2" useGeneratedKeys="true" keyProperty="id">
        insert into sc_smc_listing_amazon_attribute_line_v2
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="headId != null">head_id,</if>
            <if test="pdmGoodsCode != null">pdm_goods_code,</if>
            <if test="productType != null and productType != ''">product_type,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="propNodePath != null and propNodePath != ''">prop_node_path,</if>
            <if test="tableValue != null">table_value,</if>
            <if test="tableName != null">table_name,</if>
            <if test="tableType != null">table_type,</if>
            <if test="propertyGroupId != null">property_group_id,</if>
            <if test="vcFlag != null">vc_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="headId != null">#{headId},</if>
            <if test="pdmGoodsCode != null">#{pdmGoodsCode},</if>
            <if test="productType != null and productType != ''">#{productType},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="propNodePath != null and propNodePath != ''">#{propNodePath},</if>
            <if test="tableValue != null">#{tableValue},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="tableType != null">#{tableType},</if>
            <if test="propertyGroupId != null">#{propertyGroupId},</if>
            <if test="vcFlag != null">#{vcFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateListingAmazonAttributeLineV2" parameterType="ListingAmazonAttributeLineV2">
        update sc_smc_listing_amazon_attribute_line_v2
        <trim prefix="SET" suffixOverrides=",">
            <if test="headId != null">head_id = #{headId},</if>
            <if test="pdmGoodsCode != null">pdm_goods_code = #{pdmGoodsCode},</if>
            <if test="productType != null and productType != ''">product_type = #{productType},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="propNodePath != null and propNodePath != ''">prop_node_path = #{propNodePath},</if>
            <if test="tableValue != null">table_value = #{tableValue},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="tableType != null">table_type = #{tableType},</if>
            <if test="propertyGroupId != null">property_group_id = #{propertyGroupId},</if>
            <if test="vcFlag != null">vc_flag = #{vcFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateCategoryIdByGoodsId">
        update sc_smc_listing_amazon_attribute_line_v2
        set category_id = #{categoryId},update_time = now()
        where head_id = #{goodsId}
    </update>

    <delete id="deleteListingAmazonAttributeLineV2ById" parameterType="Long">
        delete from sc_smc_listing_amazon_attribute_line_v2 where id = #{id}
    </delete>

    <delete id="deleteListingAmazonAttributeLineV2ByIds" parameterType="String">
        delete from sc_smc_listing_amazon_attribute_line_v2 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByGoodsId">
        delete from sc_smc_listing_amazon_attribute_line_v2 where head_id = #{headId}
    </delete>
    <delete id="deleteListingAmazonAttributeLineV2ByGoodId">
        delete from sc_smc_listing_amazon_attribute_line_v2 where head_id = #{goodsId}
        and table_type in
         <foreach collection="tableTypes" item="tableType" open="(" separator="," close=")">
            #{tableType}
         </foreach>
        <if test="propNodePaths != null and propNodePaths.size() > 0">
            and prop_node_path not in
            <foreach collection="propNodePaths" item="propNodePath" open="(" separator="," close=")">
                #{propNodePath}
            </foreach>
        </if>
    </delete>
    <delete id="deleteGTINByGoodId">
        delete from sc_smc_listing_amazon_attribute_line_v2 where head_id = #{goodsId} and table_type = 4
        and prop_node_path in ('externally_assigned_product_identifier.value', 'external_product_id.value', 'external_product_id.type', 'externally_assigned_product_identifier.type')
    </delete>
    <delete id="deleteListingAmazonAttributeLineV2ByGoodIdAndPathList">
        delete from sc_smc_listing_amazon_attribute_line_v2 where head_id = #{goodsId} and prop_node_path in
        <foreach collection="deleteAttrNodePathList" item="deleteAttrNodePath" open="(" separator="," close=")">
            #{deleteAttrNodePath}
        </foreach>
    </delete>

</mapper>