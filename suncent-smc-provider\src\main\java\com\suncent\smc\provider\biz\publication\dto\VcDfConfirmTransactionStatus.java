package com.suncent.smc.provider.biz.publication.dto;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * vc请求更新 结果返回实体
 */
@Data
public class VcDfConfirmTransactionStatus {

    private String transactionId;

    private String status;

    private List<ErrorDetails> errors;

    @Data
    public static class ErrorDetails {
        private String code;

        private String message;

        private String details;

    }

    /**
     * VC库存更新错误枚举
     */
    @Getter
    @AllArgsConstructor
    public enum VcDfInventoryUpdateErrorEnum {

        //库存抑制
        ITEM_SUPPRESSED("ITEM_SUPPRESSED", "库存抑制"),
        //仓库暂停
        WAREHOUSE_IS_SUSPENDED("WAREHOUSE_IS_SUSPENDED", "仓库暂停");

        private String code;
        private String desc;


        private List<String> getErrors() {
            return Lists.newArrayList(ITEM_SUPPRESSED.code, WAREHOUSE_IS_SUSPENDED.code);

        }

    }

}