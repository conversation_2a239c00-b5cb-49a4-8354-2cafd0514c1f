package com.suncent.smc.web.controller.promotion;

import com.suncent.smc.common.core.controller.BaseController;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.page.TableDataInfo;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.provider.biz.publication.AmazonApiHttpRequestBiz;
import com.suncent.smc.web.controller.promotion.dto.AsinSelectionDTO;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 促销工具Controller - 提供辅助数据API
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Controller
@RequestMapping("/promotion/tool")
public class PromotionToolController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(PromotionToolController.class);

    @Autowired
    private IGoodsHeadService goodsHeadService;

    @Autowired
    private AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;

    /**
     * 获取可用的ASIN列表 - 用于ASIN选择器
     */
    @RequiresPermissions("promotion:tool:asin")
    @PostMapping("/listAvailableAsins")
    @ResponseBody
    public TableDataInfo listAvailableAsins(
            @RequestParam(value = "publishType") Integer publishType,
            @RequestParam(value = "search", required = false) String search,
            @RequestParam(value = "shopCode", required = false) String shopCode) {
        
        startPage();

        // 构建查询条件
        GoodsHead queryCondition = new GoodsHead();
        queryCondition.setPlatform("AM"); // 只查询Amazon平台的商品
        queryCondition.setDelFlag(0); // 未删除的商品
        queryCondition.setPublishType(5);

        // 设置搜索条件（支持ASIN、商品编码、标题搜索）
        if (search != null && !search.isEmpty()) {
            // 支持多种搜索方式：ASIN、平台SKU、PDM商品编码、标题
            // 由于GoodsHead没有setSearchKeyword方法，我们使用具体的字段进行搜索
            // 这里假设搜索关键字可能是ASIN，设置到platformGoodsId字段
            queryCondition.setPlatformGoodsId(search);
        }
        
        // 只查询已发布的商品
//        queryCondition.setPublishStatus(3); // 3表示已发布状态
        
        List<GoodsHead> list = goodsHeadService.listVCAvailableAsins(queryCondition);
        return getDataTable(list);
    }

    /**
     * 根据ASIN获取商品详细信息
     */
    @RequiresPermissions("promotion:tool:asin")
    @GetMapping("/getAsinDetail/{asin}")
    @ResponseBody
    public GoodsHead getAsinDetail(@PathVariable("asin") String asin) {
        GoodsHead queryCondition = new GoodsHead();
        queryCondition.setPlatformGoodsId(asin);
        queryCondition.setPlatform("AM");
        queryCondition.setDelFlag(0);
        
        List<GoodsHead> list = goodsHeadService.selectListingGoodsHeadList(queryCondition);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 批量获取ASIN信息
     */
    @RequiresPermissions("promotion:tool:asin")
    @PostMapping("/batchGetAsinInfo")
    @ResponseBody
    public List<GoodsHead> batchGetAsinInfo(@RequestBody List<String> asinList) {
        if (asinList == null || asinList.isEmpty()) {
            return null;
        }

        return goodsHeadService.selectGoodsHeadByAsin(asinList, "AM");
    }


    /**
     * 获取站点列表
     */
    @RequiresPermissions("promotion:record:view")
    @GetMapping("/getSiteList")
    @ResponseBody
    public String[] getSiteList() {
        // 返回Amazon支持的站点列表
        return new String[]{"US", "UK", "DE", "FR", "IT", "ES", "CA", "JP", "AU", "IN", "MX", "BR"};
    }

    /**
     * 获取店铺列表 - 根据站点筛选
     */
    @RequiresPermissions("promotion:record:view")
    @GetMapping("/getShopList")
    @ResponseBody
    public List<String> getShopList(@RequestParam(value = "site", required = false) String site) {
        // 构建查询条件获取店铺列表
        GoodsHead queryCondition = new GoodsHead();
        queryCondition.setPlatform("AM");
        queryCondition.setDelFlag(0);
        if (site != null && !site.isEmpty()) {
            queryCondition.setSiteCode(site);
        }

        // 查询所有符合条件的商品，然后提取不重复的店铺编码
        List<GoodsHead> goodsList = goodsHeadService.selectListingGoodsHeadList(queryCondition);
        return goodsList.stream()
                .map(GoodsHead::getShopCode)
                .filter(shopCode -> shopCode != null && !shopCode.isEmpty())
                .distinct()
                .sorted()
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 转换GoodsHead为AsinSelectionDTO
     */
    private AsinSelectionDTO convertToAsinSelectionDTO(GoodsHead goodsHead) {
        AsinSelectionDTO dto = new AsinSelectionDTO();

        dto.setAsin(goodsHead.getPlatformGoodsId());
        dto.setPlatformGoodsCode(goodsHead.getPlatformGoodsCode());
        dto.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        dto.setTitle(goodsHead.getTitle());
        dto.setShopCode(goodsHead.getShopCode());
        dto.setSiteCode(goodsHead.getSiteCode());
        dto.setPublishType(goodsHead.getPublishType());
        dto.setPublishStatus(goodsHead.getPublishStatus());

        // 修复字段映射 - 使用GoodsHead中实际存在的字段
        if (goodsHead.getStandardPrice() != null) {
            try {
                dto.setSalePrice(new java.math.BigDecimal(goodsHead.getStandardPrice()));
            } catch (NumberFormatException e) {
                dto.setSalePrice(java.math.BigDecimal.ZERO);
            }
        }

        // 库存字段映射
        if (goodsHead.getStockOnSalesQty() != null) {
            dto.setInventory(goodsHead.getStockOnSalesQty().intValue());
        }

        // 品牌字段映射
        dto.setBrand(goodsHead.getBrandCode());
        dto.setCategoryName(goodsHead.getCategoryName());

        // 格式化时间
        if (goodsHead.getCreateTime() != null) {
            dto.setCreateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, goodsHead.getCreateTime()));
        }
        if (goodsHead.getUpdateTime() != null) {
            dto.setUpdateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, goodsHead.getUpdateTime()));
        }

        return dto;
    }

    /**
     * 验证ASIN有效性
     */
    @RequiresPermissions("promotion:tool:asin")
    @PostMapping("/validateAsins")
    @ResponseBody
    public AjaxResult validateAsins(@RequestBody List<String> asins) {
        try {
            if (asins == null || asins.isEmpty()) {
                return AjaxResult.error("ASIN列表不能为空");
            }

            Map<String, Object> result = new HashMap<>();
            List<String> validAsins = new ArrayList<>();
            List<String> invalidAsins = new ArrayList<>();
            List<String> existingAsins = new ArrayList<>();
            List<String> notFoundAsins = new ArrayList<>();

            for (String asin : asins) {
                if (StringUtils.isNotBlank(asin)) {
                    String trimmedAsin = asin.trim();

                    // 验证ASIN格式
                    if (!isValidAsinFormat(trimmedAsin)) {
                        invalidAsins.add(trimmedAsin);
                        continue;
                    }

                    // 检查ASIN是否存在于商品库中
                    GoodsHead goods = getAsinDetail(trimmedAsin);
                    if (goods != null) {
                        validAsins.add(trimmedAsin);
                        existingAsins.add(trimmedAsin);
                    } else {
                        validAsins.add(trimmedAsin);
                        notFoundAsins.add(trimmedAsin);
                    }
                }
            }

            result.put("validAsins", validAsins);
            result.put("invalidAsins", invalidAsins);
            result.put("existingAsins", existingAsins);
            result.put("notFoundAsins", notFoundAsins);
            result.put("validCount", validAsins.size());
            result.put("invalidCount", invalidAsins.size());
            result.put("existingCount", existingAsins.size());
            result.put("notFoundCount", notFoundAsins.size());

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("验证ASIN有效性失败", e);
            return AjaxResult.error("验证ASIN有效性失败：" + e.getMessage());
        }
    }

    /**
     * 计算促销价格
     */
    @RequiresPermissions("promotion:tool:calculate")
    @PostMapping("/calculatePromotionPrice")
    @ResponseBody
    public AjaxResult calculatePromotionPrice(@RequestParam("referencePrice") BigDecimal referencePrice,
                                            @RequestParam("discountPercent") BigDecimal discountPercent) {
        try {
            if (referencePrice == null || referencePrice.compareTo(BigDecimal.ZERO) <= 0) {
                return AjaxResult.error("引用价格必须大于0");
            }

            if (discountPercent == null || discountPercent.compareTo(BigDecimal.ZERO) < 0 ||
                discountPercent.compareTo(new BigDecimal("100")) > 0) {
                return AjaxResult.error("折扣百分比必须在0-100之间");
            }

            // 计算促销价格
            BigDecimal discountAmount = referencePrice.multiply(discountPercent).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal promotionPrice = referencePrice.subtract(discountAmount);

            Map<String, Object> result = new HashMap<>();
            result.put("referencePrice", referencePrice);
            result.put("discountPercent", discountPercent);
            result.put("discountAmount", discountAmount);
            result.put("promotionPrice", promotionPrice);
            result.put("savings", discountAmount);

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("计算促销价格失败", e);
            return AjaxResult.error("计算促销价格失败：" + e.getMessage());
        }
    }

    /**
     * 获取站点配置信息
     */
    @RequiresPermissions("promotion:record:view")
    @PostMapping("/getSiteConfig")
    @ResponseBody
    public AjaxResult getSiteConfig(@RequestParam("site") String site) {
        try {
            Map<String, Object> siteConfig = generateSiteConfig(site);
            return AjaxResult.success(siteConfig);

        } catch (Exception e) {
            logger.error("获取站点配置失败", e);
            return AjaxResult.error("获取站点配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取促销模板
     */
    @RequiresPermissions("promotion:tool:template")
    @PostMapping("/getPromotionTemplates")
    @ResponseBody
    public AjaxResult getPromotionTemplates(@RequestParam(value = "type", required = false) String type) {
        try {
            List<Map<String, Object>> templates = generatePromotionTemplates(type);
            return AjaxResult.success(templates);

        } catch (Exception e) {
            logger.error("获取促销模板失败", e);
            return AjaxResult.error("获取促销模板失败：" + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 验证ASIN格式
     */
    private boolean isValidAsinFormat(String asin) {
        if (StringUtils.isBlank(asin)) {
            return false;
        }

        // ASIN格式：B + 9位字母数字组合
        return asin.matches("^B[0-9A-Z]{9}$");
    }

    /**
     * 生成站点配置
     */
    private Map<String, Object> generateSiteConfig(String site) {
        Map<String, Object> config = new HashMap<>();

        config.put("site", site);
        config.put("currency", getCurrencyBySite(site));
        config.put("language", getLanguageBySite(site));
        config.put("timezone", getTimezoneBySite(site));
        config.put("marketplace", "amazon." + site.toLowerCase());

        // 促销相关配置
        Map<String, Object> promotionConfig = new HashMap<>();
        promotionConfig.put("maxDiscountPercent", new BigDecimal("70"));
        promotionConfig.put("minPromotionDays", 7);
        promotionConfig.put("maxPromotionDays", 90);
        promotionConfig.put("maxAsinCount", 50);

        config.put("promotionConfig", promotionConfig);

        return config;
    }

    /**
     * 生成促销模板
     */
    private List<Map<String, Object>> generatePromotionTemplates(String type) {
        List<Map<String, Object>> templates = new ArrayList<>();

        // 模拟促销模板
        Map<String, Object> template1 = new HashMap<>();
        template1.put("id", 1);
        template1.put("name", "春季促销模板");
        template1.put("type", "SEASONAL");
        template1.put("description", "适用于春季商品的促销模板");
        template1.put("defaultDiscount", new BigDecimal("25"));
        template1.put("duration", 30);
        templates.add(template1);

        Map<String, Object> template2 = new HashMap<>();
        template2.put("id", 2);
        template2.put("name", "清仓促销模板");
        template2.put("type", "CLEARANCE");
        template2.put("description", "适用于清仓商品的促销模板");
        template2.put("defaultDiscount", new BigDecimal("50"));
        template2.put("duration", 14);
        templates.add(template2);

        Map<String, Object> template3 = new HashMap<>();
        template3.put("id", 3);
        template3.put("name", "新品推广模板");
        template3.put("type", "NEW_PRODUCT");
        template3.put("description", "适用于新品推广的促销模板");
        template3.put("defaultDiscount", new BigDecimal("15"));
        template3.put("duration", 60);
        templates.add(template3);

        // 如果指定了类型，进行过滤
        if (StringUtils.isNotBlank(type)) {
            templates = templates.stream()
                    .filter(t -> type.equals(t.get("type")))
                    .collect(ArrayList::new, (list, item) -> list.add(item), ArrayList::addAll);
        }

        return templates;
    }

    /**
     * 根据站点获取货币
     */
    private String getCurrencyBySite(String site) {
        switch (site.toUpperCase()) {
            case "US": return "USD";
            case "UK": return "GBP";
            case "DE":
            case "FR":
            case "IT":
            case "ES": return "EUR";
            case "JP": return "JPY";
            case "CA": return "CAD";
            default: return "USD";
        }
    }

    /**
     * 根据站点获取语言
     */
    private String getLanguageBySite(String site) {
        switch (site.toUpperCase()) {
            case "US":
            case "CA":
            case "UK": return "en";
            case "DE": return "de";
            case "FR": return "fr";
            case "IT": return "it";
            case "ES": return "es";
            case "JP": return "ja";
            default: return "en";
        }
    }

    /**
     * 根据站点获取时区
     */
    private String getTimezoneBySite(String site) {
        switch (site.toUpperCase()) {
            case "US": return "America/New_York";
            case "UK": return "Europe/London";
            case "DE": return "Europe/Berlin";
            case "FR": return "Europe/Paris";
            case "IT": return "Europe/Rome";
            case "ES": return "Europe/Madrid";
            case "JP": return "Asia/Tokyo";
            case "CA": return "America/Toronto";
            default: return "UTC";
        }
    }

    /**
     * 批量获取ASIN竞争摘要信息（包含价格限制）
     * 调用Amazon competitiveSummary接口获取真实的价格限制数据
     */
    @RequiresPermissions("promotion:tool:asin")
    @PostMapping("/batchGetCompetitiveSummary")
    @ResponseBody
    public AjaxResult batchGetCompetitiveSummary(@RequestBody Map<String, Object> requestData) {
        List<String> asinList = (List<String>) requestData.get("asinList");
        String shopCode = (String) requestData.get("shopCode");

        if (asinList == null || asinList.isEmpty()) {
            return AjaxResult.error("ASIN列表不能为空");
        }

        if (StringUtils.isBlank(shopCode)) {
            return AjaxResult.error("店铺编码不能为空");
        }

        try {
            logger.info("获取ASIN竞争摘要信息，店铺：{}，ASIN数量：{}", shopCode, asinList.size());

            // 调用Amazon competitiveSummary接口
            CompletableFuture<AjaxResult> future = amazonApiHttpRequestBiz
                .getCompetitiveSummaryResultAsync(shopCode, asinList);

            AjaxResult result = future.get(30, TimeUnit.SECONDS);

            if (result.isSuccess()) {
                logger.info("获取ASIN竞争摘要信息成功，店铺：{}，ASIN数量：{}", shopCode, asinList.size());
                return result;
            } else {
                logger.error("获取竞争摘要信息失败，店铺：{}，错误信息：{}", shopCode, result.get(AjaxResult.MSG_TAG));
                return result;
            }

        } catch (Exception e) {
            logger.error("获取竞争摘要信息异常，店铺：{}，ASIN列表：{}", shopCode, asinList, e);
            return AjaxResult.error("获取竞争摘要信息失败：" + e.getMessage());
        }
    }
}
