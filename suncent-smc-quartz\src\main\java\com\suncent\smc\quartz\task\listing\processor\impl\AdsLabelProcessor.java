package com.suncent.smc.quartz.task.listing.processor.impl;

import cn.hutool.core.util.ObjUtil;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.ads.domain.AdsListingLabel;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.quartz.task.listing.processor.LabelProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ADS标签处理器（不包含购物车标签）
 * 处理ADS数据组标签数据，生成相关标签
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Component
@Slf4j
public class AdsLabelProcessor implements LabelProcessor {

    @Autowired
    private IAdsService adsService;

    @Autowired
    private IGoodsHeadService goodsHeadService;

    @Override
    public String getLabelType() {
        return "ads";
    }

    @Override
    public List<AdsListingLabel> querySourceData(String shopCode, int offset, int limit) {
        return adsService.getListingLabelListPaged(shopCode, offset, limit);
    }

    @Override
    public List<ListingLabel> processToLabels(List<? extends Object> sourceData, String shopCode) {
        List<AdsListingLabel> adsLabelList = (List<AdsListingLabel>) sourceData;

        if (ObjUtil.isEmpty(adsLabelList)) {
            log.debug("店铺 {} 的ADS标签数据为空", shopCode);
            return Collections.emptyList();
        }

        // 设置标签数据的头表ID（不包含购物车数据处理）
        setListingLabelAds(adsLabelList, shopCode);

        List<ListingLabel> dbLabelList = new ArrayList<>();

        // 处理标签列表
        for (AdsListingLabel label : adsLabelList) {
            if (ObjUtil.isNotEmpty(label.getHeadId())) {
                // 价格标签
                addLabel(dbLabelList, label, label.getPriceLabel());

                // 链接质量标签
                if (ObjUtil.isNotEmpty(label.getListingLabel())) {
                    addLabel(dbLabelList, label, "链接质量" + label.getListingLabel());
                }

                // ADS性能标签
                addLabels(dbLabelList, label, label.getAdsPerformanceLabel());

                // VC标签
                addLabels(dbLabelList, label, label.getVcLabel());
            }
        }

        return filterAndDeduplicateLabels(dbLabelList);
    }

    @Override
    public int getBatchSize() {
        return 500;
    }

    @Override
    public int getPriority() {
        return 2;
    }

    @Override
    public String getDescription() {
        return "ADS数据组标签处理器";
    }

    @Override
    public Set<String> getDeleteConditions() {
        // ADS标签处理器删除时需要排除"有购物车"标签
        // 返回排除条件，表示 NOT IN ("有购物车")
        return Collections.singleton("有购物车");
    }

    @Override
    public boolean isExcludeMode() {
        // 返回true表示getDeleteConditions()返回的是排除条件（NOT IN）
        return true;
    }

    /**
     * 设置ADS标签数据的头表ID（不包含购物车数据处理）
     */
    private void setListingLabelAds(List<AdsListingLabel> listingLabelList, String shopCode) {
        // 获取商品头表数据
        List<GoodsHeadVO> goodsHeadVOList = goodsHeadService.selectListingByShopCode(Collections.singletonList(shopCode));

        if (ObjUtil.isEmpty(goodsHeadVOList)) {
            log.warn("店铺 {} 的ADS数据组标签无对应的SMC商品主数据", shopCode);
            return;
        }

        // 构建映射关系
        HashMap<String, Integer> goodsHeadVOMap = goodsHeadVOList.stream()
                .collect(Collectors.toMap(
                        goodsHeadVO -> goodsHeadVO.getShopCode() + goodsHeadVO.getPdmGoodsCode() +
                                goodsHeadVO.getPlatformGoodsCode() + goodsHeadVO.getPlatformGoodsId(),
                        GoodsHeadVO::getId,
                        (k1, k2) -> k1,
                        HashMap::new
                ));

        // 设置标签指标数据的头表ID
        listingLabelList.forEach(label -> {
            String key = label.getShopCode() + label.getSku() + label.getPlatformSku() + label.getAsin();
            Integer id = goodsHeadVOMap.get(key);
            if (ObjUtil.isNotEmpty(id)) {
                label.setHeadId(id);
            }
        });
    }

    /**
     * 添加单个标签
     */
    private void addLabel(List<ListingLabel> dbLabelList, AdsListingLabel adsLabel, String label) {
        if (ObjUtil.isNotEmpty(label)) {
            ListingLabel listingLabel = new ListingLabel();
            listingLabel.setHeadId(adsLabel.getHeadId());
            listingLabel.setPlatform(adsLabel.getPlatformCode());
            listingLabel.setSiteCode(adsLabel.getSitCode());
            listingLabel.setShopCode(adsLabel.getShopCode());
            listingLabel.setLabelType(getLabelType());
            listingLabel.setLabel(label);
            listingLabel.setCreateTime(DateUtils.getNowDate());
            dbLabelList.add(listingLabel);
        }
    }

    /**
     * 添加多个标签（用/分隔）
     */
    private void addLabels(List<ListingLabel> dbLabelList, AdsListingLabel adsLabel, String labels) {
        if (ObjUtil.isNotEmpty(labels)) {
            for (String label : labels.split("/")) {
                addLabel(dbLabelList, adsLabel, label);
            }
        }
    }

    /**
     * 过滤和去重标签
     */
    private List<ListingLabel> filterAndDeduplicateLabels(List<ListingLabel> dbLabelList) {
        return new ArrayList<>(dbLabelList.stream()
                .filter(label -> ObjUtil.isNotEmpty(label.getLabel()))
                .collect(Collectors.toMap(
                        label -> label.getHeadId() + "_" + label.getLabel(),
                        label -> label,
                        (existing, replacement) -> existing
                ))
                .values());
    }
}
