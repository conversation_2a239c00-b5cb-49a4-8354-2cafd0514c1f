package com.suncent.smc.quartz.task.bi;

import com.suncent.smc.persistence.configuration.biData.domain.entity.DailySalesForecast;
import com.suncent.smc.persistence.configuration.biData.service.IDailySalesForecastService;
import com.suncent.smc.provider.biz.publication.BIHttpRequestBiz;
import com.suncent.smc.provider.biz.publication.dto.ForecastDataRow;
import com.suncent.smc.provider.biz.publication.dto.ForecastResponse;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * bi 日销定时任务
 * bi 预测商品未来350天日销数据
 */
@Slf4j
@Component
public class DailySalesForecastTask {
    @Autowired
    IDailySalesForecastService dailySalesForecastService;
    @Autowired
    BIHttpRequestBiz biHttpRequestBiz;

    @XxlJob("dailySalesForecastTask")
    public void dailySalesForecastTask() {
        try {
            syncData();
        } catch (Exception e) {
            log.error("bi日销数据同步异常", e);
        }
    }

    private void syncData() {
        int pageNum = 0, pageSize = 1000; // 注意：新接口的pageNum从0开始
        boolean hasMoreData = true;

        while (hasMoreData) {
            ForecastResponse response = biHttpRequestBiz.fetchForecast(pageNum, pageSize);
            if (response == null || response.getData() == null) {
                log.warn("获取日销预测数据失败，pageNum: {}", pageNum);
                break;
            }

            List<ForecastDataRow> rows = response.getData().getRows();
            if (rows == null || rows.isEmpty()) {
                log.info("第{}页没有更多数据，结束同步", pageNum);
                hasMoreData = false;
                break;
            }

            log.info("正在处理第{}页数据，共{}条记录", pageNum, rows.size());

            for (ForecastDataRow row : rows) {
                try {
                    DailySalesForecast forecast = convert(row);
                    dailySalesForecastService.insertDailySalesForecast(forecast);
                } catch (Exception e) {
                    log.error("bi日销数据重复记录：sku={}, region={}, channel={}, forecastDate={}",
                            row.getSku(), row.getRegion(), row.getChannel(), row.getForecastDate());
                }
            }

            // 如果返回的数据量小于页面大小，说明已经是最后一页
            if (rows.size() < pageSize) {
                hasMoreData = false;
            }

            pageNum++;
        }

        log.info("日销预测数据同步完成");
    }


    private DailySalesForecast convert(ForecastDataRow row) {
        DailySalesForecast entity = new DailySalesForecast();
        entity.setPeriodIdD(Date.from(LocalDate.parse(row.getPeriodIdD()).atStartOfDay(ZoneId.systemDefault()).toInstant()));
        entity.setForecastDate(Date.from(LocalDate.parse(row.getForecastDate()).atStartOfDay(ZoneId.systemDefault()).toInstant()));
        entity.setPlatformSku(row.getPlatformSku());
        entity.setSku(row.getSku());
        entity.setChannel(row.getChannel());
        entity.setRegion(row.getRegion());
        entity.setClassificationName(row.getClassificationName());
        entity.setOperationClassification(row.getOperationClassification());
        entity.setFactorsSalesQty(row.getFactorsSalesQty());
        entity.setDeliverMethod(row.getDeliverMethod());
        return entity;
    }

}