package com.suncent.smc.quartz.task.listing.processor.impl;

import cn.hutool.core.util.ObjUtil;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.ads.domain.AdsRecordData;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.quartz.task.listing.processor.LabelProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ADS购物车标签处理器（亚马逊专用）
 * 专门处理亚马逊平台的购物车数据，生成"有购物车"标签
 * 只允许亚马逊平台的数据进入处理流程
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Component
@Slf4j
public class AdsCartLabelProcessor implements LabelProcessor {

    @Autowired
    private IAdsService adsService;

    @Autowired
    private IGoodsHeadService goodsHeadService;

    @Override
    public String getLabelType() {
        return "ads_cart";
    }

    @Override
    public List<AdsRecordData> querySourceData(String shopCode, int offset, int limit) {
        try {
            String currentDate = DateUtils.getDate();
            // 分页查询购物车数据，避免一次性查询全量数据
            // 只查询亚马逊平台的购物车数据
            return adsService.selectTodayCartListPagedForAmazon(currentDate, shopCode, offset, limit);
        } catch (Exception e) {
            log.warn("获取店铺 {} 的今日亚马逊购物车数据失败: {}", shopCode, e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public List<ListingLabel> processToLabels(List<? extends Object> sourceData, String shopCode) {
        List<AdsRecordData> cartDataList = (List<AdsRecordData>) sourceData;

        if (ObjUtil.isEmpty(cartDataList)) {
            log.debug("店铺 {} 的购物车数据为空", shopCode);
            return Collections.emptyList();
        }

        // 获取商品头表数据
        List<GoodsHeadVO> goodsHeadVOList = goodsHeadService.selectListingByShopCode(Collections.singletonList(shopCode));

        if (ObjUtil.isEmpty(goodsHeadVOList)) {
            log.warn("店铺 {} 的购物车数据无对应的SMC商品主数据", shopCode);
            return Collections.emptyList();
        }

        // 构建购物车映射
        Map<String, Integer> cartMap = buildCartMapping(goodsHeadVOList);

        List<ListingLabel> dbLabelList = new ArrayList<>();

        // 处理购物车数据（仅亚马逊平台）
        cartDataList.forEach(data -> {
            // 验证是否为有效的亚马逊ASIN
            if (!isValidAmazonAsin(data.getPageAsin())) {
                log.debug("跳过非亚马逊ASIN数据: {}", data.getPageAsin());
                return;
            }

            Integer goodId = cartMap.get(data.getShopCode() + data.getPageAsin());
            if (ObjUtil.isNotEmpty(goodId) && ObjUtil.equals("1", data.getCart())) {
                ListingLabel listingLabel = new ListingLabel();
                listingLabel.setHeadId(goodId);
                listingLabel.setPlatform(PlatformTypeEnum.AM.name());
                listingLabel.setSiteCode(ObjUtil.isNotEmpty(data.getCountry()) ? data.getCountry().toUpperCase() : "US");
                listingLabel.setShopCode(data.getShopCode());
                listingLabel.setLabelType(getLabelType());
                listingLabel.setLabel("有购物车");
                listingLabel.setCreateTime(DateUtils.getNowDate());
                dbLabelList.add(listingLabel);
            }
        });

        return filterAndDeduplicateLabels(dbLabelList);
    }

    @Override
    public int getBatchSize() {
        return 1000; // 购物车数据可以设置更大的批次
    }

    @Override
    public int getPriority() {
        return 3; // 优先级低于ADS标签处理器
    }

    @Override
    public String getDescription() {
        return "ADS购物车标签处理器（亚马逊专用）";
    }

    @Override
    public Set<String> getDeleteConditions() {
        // 删除时需要传入"有购物车"条件
        return Collections.singleton("有购物车");
    }

    /**
     * 构建购物车映射
     */
    private Map<String, Integer> buildCartMapping(List<GoodsHeadVO> goodsHeadVOList) {
        return goodsHeadVOList.stream()
                .collect(Collectors.toMap(
                        vo -> vo.getShopCode() + vo.getPlatformGoodsId(),
                        GoodsHeadVO::getId,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 验证是否为有效的亚马逊ASIN
     * 亚马逊ASIN格式：10位字符，由大写字母和数字组成
     *
     * @param asin ASIN字符串
     * @return true-有效的亚马逊ASIN，false-无效
     */
    private boolean isValidAmazonAsin(String asin) {
        if (ObjUtil.isEmpty(asin)) {
            return false;
        }
        // 亚马逊ASIN格式验证：10位字符，由大写字母和数字组成
        return asin.length() == 10 && asin.matches("^[A-Z0-9]{10}$");
    }

    /**
     * 过滤和去重标签
     */
    private List<ListingLabel> filterAndDeduplicateLabels(List<ListingLabel> dbLabelList) {
        return new ArrayList<>(dbLabelList.stream()
                .filter(label -> ObjUtil.isNotEmpty(label.getLabel()))
                .collect(Collectors.toMap(
                        label -> label.getHeadId() + "_" + label.getLabel(),
                        label -> label,
                        (existing, replacement) -> existing
                ))
                .values());
    }
}
