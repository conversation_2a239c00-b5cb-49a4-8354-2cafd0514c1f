<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('BD促销添加')" />
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        /* Tab容器样式优化 */
        .tabs-container {
            margin-top: 15px;
        }

        .tabs-container .tab-content {
            background: #fff;
            border: 1px solid #e7eaec;
            border-top: none;
            padding: 15px;
            min-height: 500px;
        }

        .tabs-container .panel-body {
            background: transparent;
            border: none;
            padding: 0;
        }

        /* Tab标签中的计数徽章样式 */
        #selected-count-badge {
            margin-left: 5px;
            font-size: 11px;
            padding: 2px 6px;
        }

        /* 表格容器样式 */
        .table-container {
            margin-top: 10px;
        }

        /* 已选ASIN操作按钮区域 */
        .selected-actions {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e7eaec;
        }

        /* 待添加ASIN操作按钮区域 */
        .available-actions {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e7eaec;
        }

        /* 确保表格在Tab中正常显示 */
        .tabs-container .table {
            margin-bottom: 0;
        }

        /* 搜索框样式 */
        .search-container {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e7eaec;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-record-add">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">促销名称：</label>
                <div class="col-sm-8">
                    <input name="promotionName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">站点：</label>
                <div class="col-sm-8">
                    <select name="site" class="form-control " readonly disabled >
                        <option value="">请选择站点</option>
                        <option value="US" selected>美国站</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">刊登类型：</label>
                <div class="col-sm-8">
                    <select name="publishType" class="form-control" required>
                        <option value="5" selected>VCDF</option>
                        <option value="6">VCPO</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">事件类型：</label>
                <div class="col-sm-8">
                    <select name="eventType" class="form-control" required onchange="handleEventTypeChange()">
                        <option value="1" selected>自定义日期</option>
                        <option value="2">会员日</option>
                        <option value="3">黑五</option>
                    </select>
                    <span class="help-block">
                        <small class="text-muted">
                            自定义日期：默认折扣10% | 会员日/黑五：默认折扣15%
                        </small>
                    </span>
                </div>
            </div>
            <div class="form-group" id="date-range-group">
                <label class="col-sm-3 control-label is-required">开始时间：</label>
                <div class="col-sm-8">
                    <input name="startDateUtc" id="startDateUtc" class="form-control" placeholder="开始时间" type="text" required readonly>
                </div>
            </div>
            <div class="form-group" id="end-date-group">
                <label class="col-sm-3 control-label is-required">结束时间：</label>
                <div class="col-sm-8">
                    <input name="endDateUtc" id="endDateUtc" class="form-control" placeholder="结束时间" type="text" required readonly>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="3"></textarea>
                </div>
            </div>
        </form>
        
        <!-- ASIN选择器 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>ASIN选择器</h5>
                        <div class="ibox-tools">
                            <span class="label label-info" id="selected-count">已选择: 0</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <!-- 搜索框 -->
                        <div class="row search-container">
                            <div class="col-sm-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="available-search" placeholder="搜索ASIN...">
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" type="button" onclick="searchAvailableAsins()">
                                            <i class="fa fa-search"></i> 搜索
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Tab导航 -->
                        <div class="tabs-container">
                            <ul class="nav nav-tabs">
                                <li class="active">
                                    <a data-toggle="tab" href="#available-tab" aria-expanded="true">待添加</a>
                                </li>
                                <li>
                                    <a data-toggle="tab" href="#selected-tab" aria-expanded="false">
                                        已添加 <span class="label label-info" id="selected-count-badge">0</span>
                                    </a>
                                </li>
                            </ul>

                            <div class="tab-content">
                                <!-- 待添加产品Tab -->
                                <div id="available-tab" class="tab-pane active">
                                    <div class="panel-body">
                                        <!-- 待添加ASIN操作按钮 -->
                                        <div class="row available-actions">
                                            <div class="col-sm-12">
                                                <button class="btn btn-sm btn-primary" onclick="addSelectedAsins()">
                                                    <i class="fa fa-plus"></i> 批量添加
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="removeSelectedFromAvailable()">
                                                    <i class="fa fa-minus"></i> 批量移除
                                                </button>
                                            </div>
                                        </div>
                                        <table id="available-table"></table>
                                    </div>
                                </div>

                                <!-- 已添加产品Tab -->
                                <div id="selected-tab" class="tab-pane">
                                    <div class="panel-body">
                                        <!-- 已选ASIN操作按钮 -->
                                        <div class="row selected-actions">
                                            <div class="col-sm-12">
                                                <button class="btn btn-sm btn-danger" onclick="removeSelectedAsins()">
                                                    <i class="fa fa-minus"></i> 批量移除
                                                </button>
                                                <button class="btn btn-sm btn-warning" onclick="clearAllAsins()">
                                                    <i class="fa fa-times"></i> 清空全部
                                                </button>
                                            </div>
                                        </div>
                                        <table id="selected-table"></table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>



    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "promotion/record";
        var toolPrefix = ctx + "promotion/tool";
        
        // 已选择的ASIN列表
        var selectedAsins = [];
        
        $(function() {
            // 初始化可选ASIN表格
            initAvailableTable();

            // 初始化已选ASIN表格
            initSelectedTable();

            // 添加Tab切换事件处理
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("href");
                if (target === '#available-tab') {
                    // 切换到待添加Tab时，刷新表格以更新按钮状态
                    refreshAvailableTable();
                } else if (target === '#selected-tab') {
                    // 切换到已添加Tab时，确保数据是最新的
                    refreshSelectedTable();
                }
            });

            // 添加表格事件处理，确保分页和搜索后按钮状态正确
            $('#available-table').on('post-body.bs.table', function () {
                // 表格数据加载完成，按钮状态会通过formatter自动更新
            });

            // 初始化表单验证
            $("#form-record-add").validate({
                rules: {
                    promotionName: {
                        required: true,
                        minlength: 2
                    },
                    publishType: {
                        required: true
                    },
                    eventType: {
                        required: true
                    },
                    startDateUtc: {
                        required: function() {
                            return $("#form-record-add select[name='eventType']").val() === '1';
                        }
                    },
                    endDateUtc: {
                        required: function() {
                            return $("#form-record-add select[name='eventType']").val() === '1';
                        }
                    }
                },
                messages: {
                    promotionName: {
                        required: "请输入促销名称",
                        minlength: "促销名称至少需要2个字符"
                    },
                    publishType: {
                        required: "请选择刊登类型"
                    },
                    eventType: {
                        required: "请选择事件类型"
                    },
                    startDateUtc: {
                        required: "自定义日期类型需要选择开始时间"
                    },
                    endDateUtc: {
                        required: "自定义日期类型需要选择结束时间"
                    }
                },
                errorElement: "span",
                errorClass: "help-block m-b-none",
                highlight: function(element) {
                    $(element).closest('.form-group').addClass('has-error');
                },
                unhighlight: function(element) {
                    $(element).closest('.form-group').removeClass('has-error');
                }
            });

            // 初始化自定义日期选择器（不依赖ry-ui）
            initCustomDatePickers();

            // 检查是否有初始数据
            loadInitialData();

            // 初始化计数显示
            updateSelectedCount();

            // 初始化事件类型显示
            handleEventTypeChange();
        });

        // 自定义日期选择器初始化（覆盖ry-ui默认行为）
        function initCustomDatePickers() {
            // 计算3天后的日期
            var today = new Date();
            var threeDaysLater = new Date();
            threeDaysLater.setDate(today.getDate() + 3);

            // 格式化为laydate需要的字符串格式
            var minDateStr = threeDaysLater.getFullYear() + '-' +
                           (threeDaysLater.getMonth() + 1).toString().padStart(2, '0') + '-' +
                           threeDaysLater.getDate().toString().padStart(2, '0');

            console.log('最小可选日期:', minDateStr);

            layui.use('laydate', function() {
                var laydate = layui.laydate;

                // 先销毁可能存在的实例
                if (window.startLayDate) {
                    console.log('销毁现有的startLayDate实例');
                    // laydate没有直接的destroy方法，我们通过重新render来覆盖
                }
                if (window.endLayDate) {
                    console.log('销毁现有的endLayDate实例');
                }

                // 开始时间选择器（完全自定义，不依赖ry-ui）
                window.startLayDate = laydate.render({
                    elem: '#startDateUtc',
                    theme: 'molv',
                    type: 'date',
                    trigger: 'click',
                    min: minDateStr, // 最早只能选择3天后
                    btns: ['clear', 'now', 'confirm'],
                    done: function(value, date) {
                        console.log('开始日期选择:', value, date);
                        // 结束时间必须大于开始时间（至少是第二天）
                        if (value !== '' && window.endLayDate) {
                            // 计算开始日期的第二天作为结束日期的最小值
                            var nextDay = new Date(date.year, date.month - 1, date.date);
                            nextDay.setDate(nextDay.getDate() + 1);

                            window.endLayDate.config.min.year = nextDay.getFullYear();
                            window.endLayDate.config.min.month = nextDay.getMonth();
                            window.endLayDate.config.min.date = nextDay.getDate();

                            console.log('更新结束日期最小值为:', nextDay.toDateString());

                            // 如果当前结束日期小于等于开始日期，清空结束日期
                            var currentEndDate = $('#endDateUtc').val();
                            if (currentEndDate && currentEndDate <= value) {
                                $('#endDateUtc').val('');
                                console.log('清空结束日期，因为当前结束日期不大于开始日期');
                            }
                        }
                    }
                });

                // 结束时间选择器（完全自定义，不依赖ry-ui）
                window.endLayDate = laydate.render({
                    elem: '#endDateUtc',
                    theme: 'molv',
                    type: 'date',
                    trigger: 'click',
                    min: minDateStr, // 最早只能选择3天后
                    btns: ['clear', 'now', 'confirm'],
                    done: function(value, date) {
                        console.log('结束日期选择:', value, date);
                        // 开始时间必须小于结束时间（至少是前一天）
                        if (value !== '' && window.startLayDate) {
                            // 计算结束日期的前一天作为开始日期的最大值
                            var prevDay = new Date(date.year, date.month - 1, date.date);
                            prevDay.setDate(prevDay.getDate() - 1);

                            window.startLayDate.config.max.year = prevDay.getFullYear();
                            window.startLayDate.config.max.month = prevDay.getMonth();
                            window.startLayDate.config.max.date = prevDay.getDate();

                            console.log('更新开始日期最大值为:', prevDay.toDateString());

                            // 如果当前开始日期大于等于结束日期，清空开始日期
                            var currentStartDate = $('#startDateUtc').val();
                            if (currentStartDate && currentStartDate >= value) {
                                $('#startDateUtc').val('');
                                console.log('清空开始日期，因为当前开始日期不小于结束日期');
                            }
                        }
                    }
                });

                console.log('完全自定义日期选择器初始化完成，最小日期:', minDateStr);
                console.log('startLayDate实例:', window.startLayDate);
                console.log('endLayDate实例:', window.endLayDate);
            });
        }

        // 加载初始数据（用于编辑模式）
        function loadInitialData() {
            var urlParams = new URLSearchParams(window.location.search);
            var storageKey = urlParams.get('storageKey');

            if (storageKey) {
                try {
                    var dataStr = sessionStorage.getItem(storageKey);
                    if (dataStr) {
                        var initialData = JSON.parse(dataStr);
                        console.log('从sessionStorage获取编辑数据:', initialData);
                        fillFormWithData(initialData);
                    } else {
                        console.warn('sessionStorage中未找到数据:', storageKey);
                    }
                } catch (e) {
                    console.error('解析sessionStorage数据失败:', e);
                }
            } else {
                console.log('没有storageKey，这是新建模式');
            }
        }

        // 处理事件类型变更
        function handleEventTypeChange() {
            var eventType = $("#form-record-add select[name='eventType']").val();
            var dateRangeGroup = $('#date-range-group');
            var endDateGroup = $('#end-date-group');
            var startDateInput = $('#startDateUtc');
            var endDateInput = $('#endDateUtc');

            if (eventType === '1') {
                // 自定义日期：显示日期选择器
                dateRangeGroup.show();
                endDateGroup.show();
                startDateInput.prop('required', true);
                endDateInput.prop('required', true);

                // 清空预设日期（如果有的话）
                if (startDateInput.val() && (startDateInput.val().includes('会员日') || startDateInput.val().includes('黑五'))) {
                    startDateInput.val('');
                    endDateInput.val('');
                }
            } else if (eventType === '2') {
                // 会员日：隐藏日期选择器，设置预设日期
                dateRangeGroup.hide();
                endDateGroup.hide();
                startDateInput.prop('required', false);
                endDateInput.prop('required', false);

                // 设置会员日的预设日期（这里可以根据实际情况调整）
                startDateInput.val('');
                endDateInput.val('');
            } else if (eventType === '3') {
                // 黑五：隐藏日期选择器，设置预设日期
                dateRangeGroup.hide();
                endDateGroup.hide();
                startDateInput.prop('required', false);
                endDateInput.prop('required', false);

                // 设置黑五的预设日期（这里可以根据实际情况调整）
                startDateInput.val('');
                endDateInput.val('');
            }

            // 重新验证表单
            $("#form-record-add").validate().resetForm();
        }

        // 用数据填充表单
        function fillFormWithData(data) {
            // 填充基本字段
            if (data.promotionName) $('#form-record-add input[name="promotionName"]').val(data.promotionName);
            if (data.publishType) $('#form-record-add select[name="publishType"]').val(data.publishType);
            if (data.eventType) $('#form-record-add select[name="eventType"]').val(data.eventType);
            if (data.status) $('#form-record-add select[name="status"]').val(data.status);
            if (data.startDateUtc) $('#form-record-add input[name="startDateUtc"]').val(data.startDateUtc);
            if (data.endDateUtc) $('#form-record-add input[name="endDateUtc"]').val(data.endDateUtc);
            if (data.remark) $('#form-record-add textarea[name="remark"]').val(data.remark);

            // 填充ASIN列表
            if (data.asinList && data.asinList.length > 0) {
                selectedAsins = data.asinList.slice(); // 复制数组
                refreshSelectedTable();
                updateSelectedCount();
            }

            // 处理事件类型变更（在数据填充后）
            handleEventTypeChange();
        }

        // 初始化可选ASIN表格
        function initAvailableTable() {
            var options = {
                id: "available-table",
                url: toolPrefix + "/listAvailableAsins",
                method: 'post',
                sidePagination: "server",
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                search: false,
                pagination: true,
                pageSize: 10,
                pageList: [10, 25, 50],
                height: 400,
                queryParams: function(params) {
                    var searchKeyword = $("#available-search").val();
                    return {
                        pageSize: params.limit,
                        pageNum: params.offset / params.limit + 1,
                        keyword: searchKeyword,
                        publishType: $('#form-record-add select[name="publishType"]').val()
                    };
                },
                columns: [{
                    checkbox: true
                },{
                    field: 'id',
                    title: '主键ID',
                    width: '10%'
                }, {
                    field: 'platformGoodsId',
                    title: 'ASIN',
                    width: '20%'
                }, {
                    field: 'platformGoodsCode',
                    title: '商品编码',
                    width: '20%'
                }, {
                    field: 'title',
                    title: '标题',
                    width: '45%',
                    formatter: function(value, row, index) {
                        return '<span title="' + value + '">' +
                               (value && value.length > 30 ? value.substring(0, 30) + '...' : value) +
                               '</span>';
                    }
                }, {
                    field: 'action',
                    title: '操作',
                    width: '15%',
                    formatter: function(value, row, index) {
                        // 判断当前ASIN是否已在已选列表中
                        var isSelected = isAsinSelected(row.id);
                        if (isSelected) {
                            return '<button class="btn btn-xs btn-danger" onclick="removeAsinFromSelected(\'' +
                                   row.id + '\')">' +
                                   '<i class="fa fa-trash"></i> Remove</button>';
                        } else {
                            return '<button class="btn btn-xs btn-primary" onclick="addAsinToSelected(\'' +
                                   row.id + '\')">' +
                                   '<i class="fa fa-plus"></i> Add</button>';
                        }
                    }
                }],
                // 移除自动添加的事件处理，改为手动批量操作
            };
            $.table.init(options);
        }

        // 初始化已选ASIN表格
        function initSelectedTable() {
            var options = {
                id: "selected-table",
                data: selectedAsins,
                sidePagination: "client",
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                search: false,
                pagination: false,
                height: 400,
                columns: [{
                    checkbox: true
                },{
                    field: 'id',
                    title: '主键ID',
                    width: '10%'
                },{
                    field: 'platformGoodsId',
                    title: 'ASIN',
                    width: '25%'
                }, {
                    field: 'platformGoodsCode',
                    title: '商品编码',
                    width: '30%'
                }, {
                    field: 'title',
                    title: '标题',
                    width: '25%',
                    formatter: function(value, row, index) {
                        return '<span title="' + value + '">' +
                               (value && value.length > 20 ? value.substring(0, 20) + '...' : value) +
                               '</span>';
                    }
                }, {
                    title: '操作',
                    width: '20%',
                    formatter: function(value, row, index) {
                        return '<button class="btn btn-xs btn-danger" onclick="removeAsinFromSelected(\'' +
                               row.id + '\')">' +
                               '<i class="fa fa-trash"></i> 移除</button>';
                    }
                }]
            };
            $.table.init(options);
        }

        // 判断ASIN是否已被选择
        function isAsinSelected(id) {
            return selectedAsins.some(function(item) {
                return item.id === id;
            });
        }

        // 刷新待添加表格（不重新请求数据，只更新显示）
        function refreshAvailableTable() {
            $('#available-table').bootstrapTable('refresh', {silent: true});
        }

        // 搜索可选ASIN
        function searchAvailableAsins() {
            $('#available-table').bootstrapTable('refresh');
        }

        // 添加ASIN到已选列表
        function addAsinToSelected(selectRecord) {
            // 检查是否已存在
            var exists = selectedAsins.some(function(item) {
                return item.id === selectRecord.id;
            });

            if (!exists) {
                selectedAsins.push(selectRecord);
                refreshSelectedTable();
                updateSelectedCount();

                // 刷新待添加表格以更新按钮状态
                refreshAvailableTable();
            }
        }

        // 从已选列表移除ASIN
        function removeAsinFromSelected(id) {
            selectedAsins = selectedAsins.filter(function(item) {
                return item.id !== id;
            });
            refreshSelectedTable();
            updateSelectedCount();

            // 刷新待添加表格以更新按钮状态
            refreshAvailableTable();
        }

        // 刷新已选表格
        function refreshSelectedTable() {
            $('#selected-table').bootstrapTable('load', selectedAsins);
        }

        // 更新已选数量显示
        function updateSelectedCount() {
            var count = selectedAsins.length;
            $('#selected-count').text('已选择: ' + count);
            $('#selected-count-badge').text(count);
        }

        // 批量添加选中的ASIN
        function addSelectedAsins() {
            var selections = $('#available-table').bootstrapTable('getSelections');
            if (selections.length === 0) {
                $.modal.alertWarning("请先选择要添加的ASIN");
                return;
            }

            for (var i = 0; i < selections.length; i++) {
                addAsinToSelected(selections[i]);
            }

            // 清除可选表格的选择状态
            $('#available-table').bootstrapTable('uncheckAll');
        }

        // 批量移除选中的ASIN（从待添加列表中移除已在已添加列表中的ASIN）
        function removeSelectedFromAvailable() {
            var selections = $('#available-table').bootstrapTable('getSelections');
            if (selections.length === 0) {
                $.modal.alertWarning("请先选择要移除的ASIN");
                return;
            }

            for (var i = 0; i < selections.length; i++) {
                removeAsinFromSelected(selections[i].id);
            }

            // 清除可选表格的选择状态
            $('#available-table').bootstrapTable('uncheckAll');
        }

        // 移除选中的ASIN
        function removeSelectedAsins() {
            var selections = $('#selected-table').bootstrapTable('getSelections');
            if (selections.length === 0) {
                $.modal.alertWarning("请先选择要移除的ASIN");
                return;
            }

            for (var i = 0; i < selections.length; i++) {
                removeAsinFromSelected(selections[i].id);
            }
        }

        // 清空所有ASIN
        function clearAllAsins() {
            if (selectedAsins.length === 0) {
                $.modal.alertWarning("没有可清空的ASIN");
                return;
            }

            $.modal.confirm("确认要清空所有已选ASIN吗？", function() {
                selectedAsins = [];
                refreshSelectedTable();
                updateSelectedCount();

                // 刷新待添加表格以更新按钮状态
                refreshAvailableTable();
            });
        }



        // 弹窗确定按钮回调函数（标准接口）
        function submitHandler(index, layero) {
            // 验证表单
            if (!$("#form-record-add").validate().form()) {
                return false; // 阻止关闭弹窗
            }

            if (selectedAsins.length === 0) {
                $.modal.alertWarning("请至少选择一个ASIN");
                return false; // 阻止关闭弹窗
            }

            // 构建提交数据
            var formData = $("#form-record-add").serializeArray();
            var submitData = {};

            // 转换表单数据
            $.each(formData, function(i, field) {
                submitData[field.name] = field.value;
            });

            // 手动添加disabled字段的值
            submitData.site = $("#form-record-add select[name='site']").val() || 'US';

            // 确保eventType是数字类型
            submitData.eventType = parseInt(submitData.eventType) || 1;

            // 添加ASIN列表
            submitData.asinList = selectedAsins;

            console.log('准备传递的数据:', submitData);

            // 将数据存储到window对象，供父页面获取
            window.dealData = submitData;

            return true; // 允许关闭弹窗
        }

        // 回车搜索
        $("#available-search").keypress(function(e) {
            if (e.which === 13) {
                searchAvailableAsins();
            }
        });
    </script>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
</body>
</html>
