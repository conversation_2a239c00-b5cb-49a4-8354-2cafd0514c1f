package com.suncent.smc.persistence.publication.service;

import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;

import java.util.List;
import java.util.Set;

/**
 * listing标签关系Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
public interface IListingLabelService 
{
    /**
     * 查询listing标签关系
     * 
     * @param id listing标签关系主键
     * @return listing标签关系
     */
    public ListingLabel selectListingLabelById(Long id);

    /**
     * 查询listing标签关系列表
     * 
     * @param listingLabel listing标签关系
     * @return listing标签关系集合
     */
    public List<ListingLabel> selectListingLabelList(ListingLabel listingLabel);

    /**
     * 新增listing标签关系
     * 
     * @param listingLabel listing标签关系
     * @return 结果
     */
    public int insertListingLabel(ListingLabel listingLabel);

    /**
     * 修改listing标签关系
     * 
     * @param listingLabel listing标签关系
     * @return 结果
     */
    public int updateListingLabel(ListingLabel listingLabel);

    /**
     * 批量删除listing标签关系
     * 
     * @param ids 需要删除的listing标签关系主键集合
     * @return 结果
     */
    public int deleteListingLabelByIds(String ids);

    /**
     * 删除listing标签关系信息
     * 
     * @param id listing标签关系主键
     * @return 结果
     */
    public int deleteListingLabelById(Long id);


    int insertListingLabelBatch(List<ListingLabel> list);

    int deleteListingLabelByShopCode(String labelType, String shopCode);

    int deleteListingLabelsByShopCode(List<String> label, String shopCode);

    int deleteListingLabelsByHeadIdsAndLabelType(List<Integer> headIds, String labelType, String shopCode);

    /**
     * 根据头表id以及标签分类查询
     * @param headIdList
     * @param listingPerformance
     * @param shopCodeList
     * @return
     */
    List<ListingLabel> selectHeadIdByListingPerformance(List<Integer> headIdList, String listingPerformance,List<String> shopCodeList);

    /**
     * 根据店铺查询购物车标签数据
     * @param shopCode
     * @return
     */
    List<ListingLabel> selectCartListingLabelList(String shopCode);

    int delYesterdayLabel(String labelType);

    public void saveBatch(List<ListingLabel> dbLabelList);

    int deleteListingLabelByLabelTypeAndLabel(String labelType, String label);

    void deleteListingLabelsByHeadIds(List<String> list, Set<Integer> headIds);

    /**
     * 根据头表ID和标签值查询标签数量
     *
     * @param headId 头表ID
     * @param label  标签值
     * @return 标签数量
     */
    int selectCountByHeadIdAndLabel(Integer headId, String label);

    /**
     * 根据头表ID和标签值删除标签
     *
     * @param headId 头表ID
     * @param label  标签值
     */
    void deleteListingLabelByHeadIdAndLabel(Integer headId, String label);

    /**
     * 根据头表ID、标签类型删除标签，排除指定的标签值
     *
     * @param headIds       头表ID列表
     * @param labelType     标签类型
     * @param excludeLabels 要排除的标签值集合
     * @param shopCode      店铺编码
     * @return 删除的标签数量
     */
    int deleteListingLabelsByHeadIdsAndLabelTypeExcluding(List<Integer> headIds, String labelType, Set<String> excludeLabels, String shopCode);
}
