package com.suncent.smc.persistence.publication.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import cn.hutool.core.util.StrUtil;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.publication.domain.entity.ListingAmazonAttributeLine;
import com.suncent.smc.persistence.publication.domain.entity.ListingAmazonAttributeLineV2;
import com.suncent.smc.persistence.publication.mapper.ListingAmazonAttributeLineV2Mapper;
import com.suncent.smc.persistence.publication.service.IListingAmazonAttributeLineV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.suncent.smc.common.core.text.Convert;

/**
 * 亚马逊商品属性信息行Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
@Slf4j
@Service
public class ListingAmazonAttributeLineV2ServiceImpl implements IListingAmazonAttributeLineV2Service
{
    @Autowired
    private ListingAmazonAttributeLineV2Mapper listingAmazonAttributeLineV2Mapper;

    /**
     * 查询亚马逊商品属性信息行
     * 
     * @param id 亚马逊商品属性信息行主键
     * @return 亚马逊商品属性信息行
     */
    @Override
    public ListingAmazonAttributeLineV2 selectListingAmazonAttributeLineV2ById(Long id)
    {
        return listingAmazonAttributeLineV2Mapper.selectListingAmazonAttributeLineV2ById(id);
    }

    /**
     * 查询亚马逊商品属性信息行列表
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 亚马逊商品属性信息行
     */
    @Override
    public List<ListingAmazonAttributeLineV2> selectListingAmazonAttributeLineV2List(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2)
    {
        return listingAmazonAttributeLineV2Mapper.selectListingAmazonAttributeLineV2List(listingAmazonAttributeLineV2);
    }

    /**
     * 新增亚马逊商品属性信息行
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 结果
     */
    @Override
    public int insertListingAmazonAttributeLineV2(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2)
    {
        listingAmazonAttributeLineV2.setCreateTime(DateUtils.getNowDate());
        if(StrUtil.isNotBlank(listingAmazonAttributeLineV2.getTableValue())) {
            listingAmazonAttributeLineV2.setTableValue(StrUtil.trim(listingAmazonAttributeLineV2.getTableValue()));
        }
        // GTIN的值在SMC统一使用大写，调用接口时统一使用小写
        if (StrUtil.isNotBlank(listingAmazonAttributeLineV2.getPropNodePath()) && "external_product_id.type".equals(listingAmazonAttributeLineV2.getPropNodePath()) && StrUtil.isNotBlank(listingAmazonAttributeLineV2.getTableValue())) {
            listingAmazonAttributeLineV2.setTableValue(listingAmazonAttributeLineV2.getTableValue().toUpperCase());
        }

        return listingAmazonAttributeLineV2Mapper.insertListingAmazonAttributeLineV2(listingAmazonAttributeLineV2);
    }

    /**
     * 修改亚马逊商品属性信息行
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 结果
     */
    @Override
    public int updateListingAmazonAttributeLineV2(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2)
    {
        listingAmazonAttributeLineV2.setUpdateTime(DateUtils.getNowDate());
        return listingAmazonAttributeLineV2Mapper.updateListingAmazonAttributeLineV2(listingAmazonAttributeLineV2);
    }

    /**
     * 批量删除亚马逊商品属性信息行
     * 
     * @param ids 需要删除的亚马逊商品属性信息行主键
     * @return 结果
     */
    @Override
    public int deleteListingAmazonAttributeLineV2ByIds(String ids)
    {
        return listingAmazonAttributeLineV2Mapper.deleteListingAmazonAttributeLineV2ByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除亚马逊商品属性信息行信息
     * 
     * @param id 亚马逊商品属性信息行主键
     * @return 结果
     */
    @Override
    public int deleteListingAmazonAttributeLineV2ById(Long id)
    {
        return listingAmazonAttributeLineV2Mapper.deleteListingAmazonAttributeLineV2ById(id);
    }

    @Override
    public List<ListingAmazonAttributeLineV2> listByGoodsId(Integer headId) {
        return listingAmazonAttributeLineV2Mapper.listByGoodsId(headId);
    }

    @Override
    public void deleteByGoodsId(Integer goodsHeadId) {
        listingAmazonAttributeLineV2Mapper.deleteByGoodsId(goodsHeadId);
    }

    @Override
    public List<ListingAmazonAttributeLineV2> selectExtendAttrByGoodsId(Integer goodsId) {
        return listingAmazonAttributeLineV2Mapper.selectExtendAttrByGoodsId(goodsId);
    }
    @Override
    public List<ListingAmazonAttributeLineV2> selecAttrByGoodsIds(List<Long> goodsId) {
        return listingAmazonAttributeLineV2Mapper.selecAttrByGoodsIds(goodsId);
    }

    @Override
    public String getPn(Integer goodsId) {
        return listingAmazonAttributeLineV2Mapper.getPn(goodsId);
    }


    @Override
    public void setPn(Integer goodsId,String newPn) {
        ListingAmazonAttributeLineV2 listingAmazonAttributeLine = new ListingAmazonAttributeLineV2();
        listingAmazonAttributeLine.setHeadId(Long.valueOf(goodsId));
        List<ListingAmazonAttributeLineV2> amazonAttributeLines = this.selectListingAmazonAttributeLineV2List(listingAmazonAttributeLine);
        Optional<ListingAmazonAttributeLineV2> attributeLine = amazonAttributeLines.stream().filter(f -> Objects.equals(f.getPropNodePath(), "part_number.value")).findFirst();
        if (attributeLine.isPresent()){
            ListingAmazonAttributeLineV2 updateLine = attributeLine.get();
            updateLine.setTableValue(newPn);
            this.updateListingAmazonAttributeLineV2(updateLine);
        }
    }


    @Override
    public ListingAmazonAttributeLineV2 getPnObj(Integer goodsId) {
        return listingAmazonAttributeLineV2Mapper.getPnObj(goodsId);
    }

    @Override
    public ListingAmazonAttributeLineV2 getAttrByPropNodePath(Integer id, String propNodePath) {
        return listingAmazonAttributeLineV2Mapper.getAttrByPropNodePath(id, propNodePath);
    }

    @Override
    public void deleteListingAmazonAttributeLineV2ByGoodId(Integer goodsId, List<String> propNodePaths, List<Integer> tableTypes) {
        listingAmazonAttributeLineV2Mapper.deleteListingAmazonAttributeLineV2ByGoodId(goodsId, propNodePaths, tableTypes);
    }

    @Override
    public void updateCategoryIdByGoodsId(Integer goodsId, Integer categoryId) {
        listingAmazonAttributeLineV2Mapper.updateCategoryIdByGoodsId(goodsId, categoryId);
    }

    @Override
    public String getFollowAsin(Integer goodsId) {
        return listingAmazonAttributeLineV2Mapper.getFollowAsin(goodsId);
    }

    @Override
    public List<ListingAmazonAttributeLineV2> getAttrByPropNodePathAndHeadIds(List<Integer> goodsId, String propNodePath) {
        return listingAmazonAttributeLineV2Mapper.getAttrByPropNodePathAndHeadIds(goodsId, propNodePath);
    }

    @Override
    public void deleteGTINByGoodId(Integer goodsId) {
        listingAmazonAttributeLineV2Mapper.deleteGTINByGoodId(goodsId);
    }

    @Override
    public void deleteListingAmazonAttributeLineV2ByGoodIdAndPathList(Integer goodsId, List<String> deleteAttrNodePathList) {
        listingAmazonAttributeLineV2Mapper.deleteListingAmazonAttributeLineV2ByGoodIdAndPathList(goodsId, deleteAttrNodePathList);
    }

    @Override
    public String getValueByPropNodePath(Integer id, String propNodePath) {
        ListingAmazonAttributeLineV2 attrByPropNodePath = getAttrByPropNodePath(id, propNodePath);
        if (attrByPropNodePath != null) {
            return attrByPropNodePath.getTableValue();
        }
        return "";
    }
}
