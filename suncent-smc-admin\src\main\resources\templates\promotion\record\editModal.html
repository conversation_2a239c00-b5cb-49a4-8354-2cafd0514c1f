<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header(${pageTitle ?: 'BD促销编辑'})" />
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        /* Tab容器样式优化 */
        .tabs-container {
            margin-top: 15px;
        }

        .tabs-container .tab-content {
            background: #fff;
            border: 1px solid #e7eaec;
            border-top: none;
            padding: 15px;
            min-height: 500px;
        }

        .tabs-container .panel-body {
            background: transparent;
            border: none;
            padding: 0;
        }

        /* Tab标签中的计数徽章样式 */
        #selected-count-badge {
            margin-left: 5px;
            font-size: 11px;
            padding: 2px 6px;
        }

        /* 表格容器样式 */
        .table-container {
            margin-top: 10px;
        }

        /* 已选ASIN操作按钮区域 */
        .selected-actions {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e7eaec;
        }

        /* 待添加ASIN操作按钮区域 */
        .available-actions {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e7eaec;
        }

        /* 确保表格在Tab中正常显示 */
        .tabs-container .table {
            margin-bottom: 0;
        }

        /* 搜索框样式 */
        .search-container {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e7eaec;
        }

        /* 价格编辑相关样式 */
        .price-cell {
            position: relative;
        }
        .price-cell input {
            margin-bottom: 5px;
            font-size: 12px;
            height: 28px;
        }
        .price-cell small {
            display: block;
            font-size: 10px;
            color: #999;
            margin-top: 2px;
        }
        .max-price {
            color: #1ab394 !important;
            font-weight: bold;
        }
        .min-funding {
            color: #f8ac59 !important;
            font-weight: bold;
        }
        .promotion-price-input.is-invalid {
            border-color: #ed5565;
            box-shadow: 0 0 0 0.2rem rgba(237, 85, 101, 0.25);
        }
        .funding-input.is-invalid {
            border-color: #ed5565;
            box-shadow: 0 0 0 0.2rem rgba(237, 85, 101, 0.25);
        }
        .invalid-feedback {
            display: block;
            font-size: 12px;
            color: #dc3545;
            margin-top: 2px;
        }

        /* 只读状态样式 */
        .form-control-readonly {
            background-color: #f5f5f5 !important;
            cursor: not-allowed !important;
            opacity: 0.8;
        }

        /* 状态提示样式 */
        .status-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-draft { background: #f8ac59; color: white; }
        .status-approved { background: #1ab394; color: white; }
        .status-needs-attention { background: #ed5565; color: white; }
        .status-canceled { background: #676a6c; color: white; }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-record-edit">
            <input name="id" type="hidden" th:value="${record?.id}">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">促销名称：</label>
                <div class="col-sm-8">
                    <input name="promotionName" class="form-control" type="text"  th:disabled="${!isDraft}" required th:value="${record?.promotionName}">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">站点：</label>
                <div class="col-sm-8">
                    <select name="site" class="form-control" readonly disabled>
                        <option value="">请选择站点</option>
                        <option value="US" th:selected="${record?.site == 'US' or record?.site == null}">美国站</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">刊登类型：</label>
                <div class="col-sm-8">
                    <select name="publishType" class="form-control" required th:disabled="${!isDraft}">
                        <option value="5" th:selected="${record?.publishType == 5 or record?.publishType == null}">VCDF</option>
                        <option value="6" th:selected="${record?.publishType == 6}">VCPO</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">事件类型：</label>
                <div class="col-sm-8">
                    <select name="eventType" class="form-control" required onchange="handleEventTypeChange()" th:disabled="${!isDraft}">
                        <option value="1" th:selected="${record?.eventType == 1 or record?.eventType == null}">自定义日期</option>
                        <option value="2" th:selected="${record?.eventType == 2}">会员日</option>
                        <option value="3" th:selected="${record?.eventType == 3}">黑五</option>
                    </select>
                    <span class="help-block">
                        <small class="text-muted">
                            自定义日期：默认折扣10% | 会员日/黑五：默认折扣15%
                        </small>
                    </span>
                </div>
            </div>
            <div class="form-group" id="date-range-group">
                <label class="col-sm-3 control-label is-required">开始时间：</label>
                <div class="col-sm-8">
                    <input name="startDateUtc" id="startDateUtc" class="form-control" placeholder="开始时间"
                           type="text" required readonly th:value="${record?.startDateUtc}" th:readonly="${!isDraft}">
                </div>
            </div>
            <div class="form-group" id="end-date-group">
                <label class="col-sm-3 control-label is-required">结束时间：</label>
                <div class="col-sm-8">
                    <input name="endDateUtc" id="endDateUtc" class="form-control" placeholder="结束时间"
                           type="text" required readonly th:value="${record?.endDateUtc}" th:readonly="${!isDraft}">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="3" th:text="${record?.remark}" th:readonly="${!isDraft}"></textarea>
                </div>
            </div>
        </form>
        
        <!-- ASIN编辑器 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>ASIN编辑器</h5>
                        <div class="ibox-tools">
                            <span class="label label-info" id="selected-count">已选择: 0</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <!-- 搜索框 -->
                        <div class="row search-container">
                            <div class="col-sm-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="available-search" placeholder="搜索ASIN...">
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" type="button" onclick="searchAvailableAsins()">
                                            <i class="fa fa-search"></i> 搜索
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Tab导航 -->
                        <div class="tabs-container">
                            <ul class="nav nav-tabs">
                                <li class="active">
                                    <a data-toggle="tab" href="#available-tab" aria-expanded="true">待添加</a>
                                </li>
                                <li>
                                    <a data-toggle="tab" href="#selected-tab" aria-expanded="false">
                                        已添加 <span class="label label-info" id="selected-count-badge">0</span>
                                    </a>
                                </li>
                            </ul>

                            <div class="tab-content">
                                <!-- 待添加产品Tab -->
                                <div id="available-tab" class="tab-pane active">
                                    <div class="panel-body">
                                        <!-- 待添加ASIN操作按钮 -->
                                        <div class="row available-actions">
                                            <div class="col-sm-12">
                                                <button class="btn btn-sm btn-primary" onclick="addSelectedAsins()">
                                                    <i class="fa fa-plus"></i> 批量添加
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="removeSelectedFromAvailable()">
                                                    <i class="fa fa-minus"></i> 批量移除
                                                </button>
                                            </div>
                                        </div>
                                        <table id="available-table"></table>
                                    </div>
                                </div>

                                <!-- 已添加产品Tab -->
                                <div id="selected-tab" class="tab-pane">
                                    <div class="panel-body">
                                        <!-- 已选ASIN操作按钮 -->
                                        <div class="row selected-actions">
                                            <div class="col-sm-12">
                                                <button class="btn btn-sm btn-danger" onclick="removeSelectedAsins()">
                                                    <i class="fa fa-minus"></i> 批量移除
                                                </button>
                                                <button class="btn btn-sm btn-warning" onclick="clearAllAsins()">
                                                    <i class="fa fa-times"></i> 清空全部
                                                </button>
                                            </div>
                                        </div>
                                        <table id="selected-table"></table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "promotion/record";
        var toolPrefix = ctx + "promotion/tool";

        // 已选择的ASIN列表
        var selectedAsins = [];

        // 从Thymeleaf获取初始数据
        var isEditMode = /*[[${isEditMode}]]*/ false;
        var isDraft = /*[[${isDraft}]]*/ true;
        var recordData = /*[[${record}]]*/ null;

        $(function() {
            // 初始化可选ASIN表格
            initAvailableTable();

            // 先加载初始数据，再初始化已选ASIN表格
            if (isEditMode && recordData) {
                loadInitialDataFromThymeleaf();
            }

            // 添加Tab切换事件处理
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("href");
                if (target === '#available-tab') {
                    refreshAvailableTable();
                } else if (target === '#selected-tab') {
                    refreshSelectedTable();
                }
            });

            // 初始化表单验证
            initFormValidation();

            // 初始化自定义日期选择器
            initCustomDatePickers();

            // 初始化计数显示
            updateSelectedCount();

            // 初始化事件类型显示
            handleEventTypeChange();

            // 根据编辑状态控制权限
            if (isEditMode) {
                controlBasicInfoEditPermission(recordData.status);
            }
        });

        // 初始化表单验证
        function initFormValidation() {
            $("#form-record-edit").validate({
                rules: {
                    promotionName: {
                        required: true,
                        minlength: 2
                    },
                    publishType: {
                        required: true
                    },
                    eventType: {
                        required: true
                    },
                    startDateUtc: {
                        required: function() {
                            return $("#form-record-edit select[name='eventType']").val() === '1';
                        }
                    },
                    endDateUtc: {
                        required: function() {
                            return $("#form-record-edit select[name='eventType']").val() === '1';
                        }
                    }
                },
                messages: {
                    promotionName: {
                        required: "请输入促销名称",
                        minlength: "促销名称至少需要2个字符"
                    },
                    publishType: {
                        required: "请选择刊登类型"
                    },
                    eventType: {
                        required: "请选择事件类型"
                    },
                    startDateUtc: {
                        required: "自定义日期类型需要选择开始时间"
                    },
                    endDateUtc: {
                        required: "自定义日期类型需要选择结束时间"
                    }
                },
                errorElement: "span",
                errorClass: "help-block m-b-none",
                highlight: function(element) {
                    $(element).closest('.form-group').addClass('has-error');
                },
                unhighlight: function(element) {
                    $(element).closest('.form-group').removeClass('has-error');
                }
            });
        }

        // 自定义日期选择器初始化
        function initCustomDatePickers() {
            var today = new Date();
            var threeDaysLater = new Date();
            threeDaysLater.setDate(today.getDate() + 3);

            var minDateStr = threeDaysLater.getFullYear() + '-' +
                           (threeDaysLater.getMonth() + 1).toString().padStart(2, '0') + '-' +
                           threeDaysLater.getDate().toString().padStart(2, '0');

            layui.use('laydate', function() {
                var laydate = layui.laydate;

                window.startLayDate = laydate.render({
                    elem: '#startDateUtc',
                    theme: 'molv',
                    type: 'date',
                    trigger: 'click',
                    min: minDateStr,
                    btns: ['clear', 'now', 'confirm'],
                    done: function(value, date) {
                        if (value !== '' && window.endLayDate) {
                            var nextDay = new Date(date.year, date.month - 1, date.date);
                            nextDay.setDate(nextDay.getDate() + 1);

                            window.endLayDate.config.min.year = nextDay.getFullYear();
                            window.endLayDate.config.min.month = nextDay.getMonth();
                            window.endLayDate.config.min.date = nextDay.getDate();

                            var currentEndDate = $('#endDateUtc').val();
                            if (currentEndDate && currentEndDate <= value) {
                                $('#endDateUtc').val('');
                            }
                        }
                    }
                });

                window.endLayDate = laydate.render({
                    elem: '#endDateUtc',
                    theme: 'molv',
                    type: 'date',
                    trigger: 'click',
                    min: minDateStr,
                    btns: ['clear', 'now', 'confirm'],
                    done: function(value, date) {
                        if (value !== '' && window.startLayDate) {
                            var prevDay = new Date(date.year, date.month - 1, date.date);
                            prevDay.setDate(prevDay.getDate() - 1);

                            window.startLayDate.config.max.year = prevDay.getFullYear();
                            window.startLayDate.config.max.month = prevDay.getMonth();
                            window.startLayDate.config.max.date = prevDay.getDate();

                            var currentStartDate = $('#startDateUtc').val();
                            if (currentStartDate && currentStartDate >= value) {
                                $('#startDateUtc').val('');
                            }
                        }
                    }
                });
            });
        }

        // 处理事件类型变更
        function handleEventTypeChange() {
            var eventType = $("#form-record-edit select[name='eventType']").val();
            var dateRangeGroup = $('#date-range-group');
            var endDateGroup = $('#end-date-group');
            var startDateInput = $('#startDateUtc');
            var endDateInput = $('#endDateUtc');

            if (eventType === '1') {
                dateRangeGroup.show();
                endDateGroup.show();
                startDateInput.prop('required', true);
                endDateInput.prop('required', true);

                if (startDateInput.val() && (startDateInput.val().includes('会员日') || startDateInput.val().includes('黑五'))) {
                    startDateInput.val('');
                    endDateInput.val('');
                }
            } else if (eventType === '2') {
                dateRangeGroup.hide();
                endDateGroup.hide();
                startDateInput.prop('required', false);
                endDateInput.prop('required', false);
                startDateInput.val('');
                endDateInput.val('');
            } else if (eventType === '3') {
                dateRangeGroup.hide();
                endDateGroup.hide();
                startDateInput.prop('required', false);
                endDateInput.prop('required', false);
                startDateInput.val('');
                endDateInput.val('');
            }

            $("#form-record-edit").validate().resetForm();
        }

        // 初始化可选ASIN表格
        function initAvailableTable() {
            var options = {
                id: "available-table",
                url: toolPrefix + "/listAvailableAsins",
                method: 'post',
                sidePagination: "server",
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                search: false,
                pagination: true,
                pageSize: 10,
                pageList: [10, 25, 50],
                height: 400,
                queryParams: function(params) {
                    var searchKeyword = $("#available-search").val();
                    return {
                        pageSize: params.limit,
                        pageNum: params.offset / params.limit + 1,
                        keyword: searchKeyword,
                        publishType: $('#form-record-edit select[name="publishType"]').val()
                    };
                },
                columns: [{
                    checkbox: true
                },{
                    field: 'id',
                    title: '主键ID',
                }, {
                    field: 'platformGoodsId',
                    title: 'ASIN',
                }, {
                    field: 'platformGoodsCode',
                    title: '平台商品编码',
                }, {
                    field: 'platformGoodsCode',
                    title: '平台商品编码',
                }, {
                    field: 'pdmGoodsCode',
                    title: '商品编码',
                }, {
                    field: 'title',
                    title: '标题',
                    formatter: function(value, row, index) {
                        return '<span title="' + value + '">' +
                               (value && value.length > 30 ? value.substring(0, 30) + '...' : value) +
                               '</span>';
                    }
                }, {
                    field: 'action',
                    title: '操作',
                    formatter: function(value, row, index) {
                        var isSelected = isAsinSelected(row.id);
                        if (isSelected) {
                            return '<button class="btn btn-xs btn-danger" onclick="removeAsinFromSelected(\'' +
                                   row.id + '\')">' +
                                   '<i class="fa fa-trash"></i> Remove</button>';
                        } else {
                            return '<button class="btn btn-xs btn-primary" onclick="addAsinToSelected(\'' +
                                   row.id + '\')">' +
                                   '<i class="fa fa-plus"></i> Add</button>';
                        }
                    }
                }]
            };
            $.table.init(options);
        }

        // 初始化已选ASIN表格（增强版，支持价格编辑）
        function initSelectedTable() {
            var options = {
                id: "selected-table",
                data: selectedAsins,
                sidePagination: "client",
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                search: false,
                pagination: false,
                height: 400,
                columns: [{
                    checkbox: true
                },{
                    field: 'id',
                    title: '主键ID',
                    width: '8%'
                },{
                    field: 'platformGoodsId',
                    title: 'ASIN',
                    width: '10%'
                }, {
                    field: 'platformGoodsCode',
                    title: '平台商品编码',
                    width: '12%'
                }, {
                    field: 'pdm_goods_code',
                    title: '商品编码',
                    width: '10%'
                }, {
                    field: 'standardPrice',
                    title: 'Cost Price',
                    width: '8%',
                    formatter: function(value, row, index) {
                        return value ? '$' + parseFloat(value).toFixed(2) : '-';
                    }
                }, {
                    field: 'expectedDemand',
                    title: 'Expected demand',
                    width: '6%'
                }, {
                    field: 'referencePrice',
                    title: 'Reference price',
                    width: '8%',
                    formatter: function(value, row, index) {
                        return value ? '$' + parseFloat(value).toFixed(2) : '-';
                    }
                }, {
                    field: 'dealPrice',
                    title: 'Deal price',
                    width: '10%',
                    formatter: function(value, row, index) {
                        return renderPromotionPriceCell(row, index);
                    }
                }, {
                    field: 'committedUnits',
                    title: 'Committed units',
                    width: '8%',
                    formatter: function(value, row, index) {
                        return renderCommittedUnitsCell(row, index);
                    }
                }, {
                    field: 'perUnitFunding',
                    title: 'Per unit funding',
                    width: '10%',
                    formatter: function(value, row, index) {
                        return renderFundingCell(row, index);
                    }
                }, {
                    field: 'discount',
                    title: 'Discount',
                    width: '7%',
                    formatter: function(value, row, index) {
                        return renderDiscountCell(row, index);
                    }
                }, {
                    title: '操作',
                    width: '8%',
                    formatter: function(value, row, index) {
                        return '<button class="btn btn-xs btn-danger" onclick="removeAsinFromSelected(\'' +
                               row.id + '\')">' +
                               '<i class="fa fa-trash"></i></button>';
                    }
                }]
            };
            $.table.init(options);
        }

        // 渲染促销价单元格（上方输入框，下方Max显示）
        function renderPromotionPriceCell(asin, index) {
            var dealPrice = parseFloat(asin.dealPrice) || parseFloat(asin.referencePrice) || 0;
            var maxPrice = parseFloat(asin.maxPrice) || 0;

            var html = '<div class="price-cell">';
            html += '<input type="number" class="form-control input-sm promotion-price-input" ';
            html += 'value="' + dealPrice.toFixed(2) + '" ';
            html += 'data-asin-index="' + index + '" ';
            html += 'onchange="updatePromotionPrice(this)" step="0.01" min="0" />';
            html += '<small class="text-muted max-price">Max: $' + maxPrice.toFixed(2) + '</small>';
            html += '</div>';

            return html;
        }

        // 渲染承诺数量单元格
        function renderCommittedUnitsCell(asin, index) {
            var committedUnits = asin.committedUnits || asin.expectedDemand || '';

            var html = '<div class="committed-units-cell">';
            html += '<input type="number" class="form-control input-sm committed-units-input" ';
            html += 'value="' + committedUnits + '" ';
            html += 'data-asin-index="' + index + '" ';
            html += 'onchange="updateCommittedUnits(this)" min="1" step="1" ';
            html += 'placeholder="承诺数量" />';
            html += '</div>';

            return html;
        }

        // 渲染funding单元格（上方输入框，下方Min显示）
        function renderFundingCell(asin, index) {
            var perUnitFunding = parseFloat(asin.perUnitFunding) || 0;
            var minFunding = parseFloat(asin.minFunding) || 0;

            var html = '<div class="price-cell">';
            html += '<input type="number" class="form-control input-sm funding-input" ';
            html += 'value="' + perUnitFunding.toFixed(2) + '" ';
            html += 'data-asin-index="' + index + '" ';
            html += 'onchange="updateFunding(this)" step="0.01" min="0" />';
            html += '<small class="text-muted min-funding">Min: $' + minFunding.toFixed(2) + '</small>';
            html += '</div>';

            return html;
        }

        // 渲染折扣单元格（显示实际折扣百分比）
        function renderDiscountCell(asin, index) {
            var referencePrice = parseFloat(asin.referencePrice) || 0;
            var dealPrice = parseFloat(asin.dealPrice) || 0;

            var discountPercent = 0;
            if (referencePrice > 0) {
                discountPercent = ((referencePrice - dealPrice) / referencePrice) * 100;
            }

            var html = '<span class="discount-display" ';
            html += 'data-asin-index="' + index + '">';
            html += discountPercent.toFixed(1) + '%';
            html += '</span>';

            return html;
        }

        // 判断ASIN是否已被选择
        function isAsinSelected(id) {
            return selectedAsins.some(function(item) {
                return item.id === id;
            });
        }

        // 刷新待添加表格
        function refreshAvailableTable() {
            $('#available-table').bootstrapTable('refresh', {silent: true});
        }

        // 搜索可选ASIN
        function searchAvailableAsins() {
            $('#available-table').bootstrapTable('refresh');
        }

        // 添加ASIN到已选列表
        function addAsinToSelected(selectRecord) {
            var exists = selectedAsins.some(function(item) {
                return item.id === selectRecord.id;
            });

            if (!exists) {
                // 为新添加的ASIN设置默认值和计算值
                initializeAsinPriceData(selectRecord);
                selectedAsins.push(selectRecord);
                refreshSelectedTable();
                updateSelectedCount();
                refreshAvailableTable();
            }
        }

        // 从已选列表移除ASIN
        function removeAsinFromSelected(id) {
            selectedAsins = selectedAsins.filter(function(item) {
                return item.id !== id;
            });
            refreshSelectedTable();
            updateSelectedCount();
            refreshAvailableTable();
        }

        // 刷新已选表格
        function refreshSelectedTable() {
            $('#selected-table').bootstrapTable('load', selectedAsins);
        }

        // 更新已选数量显示
        function updateSelectedCount() {
            var count = selectedAsins.length;
            $('#selected-count').text('已选择: ' + count);
            $('#selected-count-badge').text(count);
        }

        // 初始化ASIN价格数据
        function initializeAsinPriceData(asin) {
            var eventType = parseInt($("#form-record-edit select[name='eventType']").val()) || 1;

            // 设置临时默认值
            if (!asin.referencePrice) {
                asin.referencePrice = 19.99; // 临时默认值
            }

            // 异步获取真实价格限制信息
            var shopCode = 'VC1'

            fetchCompetitiveSummary([asin.platformGoodsId], shopCode)
                .then(function(summaryData) {
                    if (summaryData && summaryData[asin.platformGoodsId]) {
                        var competitiveInfo = summaryData[asin.platformGoodsId];

                        // 更新真实数据
                        if (competitiveInfo.referencePrice) {
                            asin.referencePrice = parseFloat(competitiveInfo.referencePrice);
                        }
                        if (competitiveInfo.maxPrice) {
                            asin.maxPrice = parseFloat(competitiveInfo.maxPrice);
                        }

                        // 重新计算价格
                        recalculateAsinPricing(asin, eventType);
                        refreshSelectedTable();
                    } else {
                        // 没有获取到数据，使用默认逻辑
                        useDefaultPricingLogic(asin, eventType);
                    }
                })
                .catch(function(error) {
                    console.warn('获取竞争摘要失败，使用默认值:', error);
                    // 失败时使用业务规则
                    useDefaultPricingLogic(asin, eventType);
                });
        }

        // 使用默认定价逻辑
        function useDefaultPricingLogic(asin, eventType) {
            // 生成模拟参考价（如果没有的话）
            if (!asin.referencePrice || asin.referencePrice <= 0) {
                asin.referencePrice = generateMockReferencePrice(asin.platformGoodsId);
            }

            // 生成模拟最高限价
            asin.maxPrice = generateMockMaxPrice(asin.platformGoodsId, asin.referencePrice);

            // 重新计算价格
            recalculateAsinPricing(asin, eventType);
        }

        // 重新计算ASIN定价
        function recalculateAsinPricing(asin, eventType) {
            // 计算最小funding（基于事件类型）
            asin.minFunding = calculateMinFundingByEventType(asin, eventType);

            // 设置默认促销价和funding
            var defaultDiscountRate = getDefaultDiscountRateByEventType(eventType);
            var targetDiscountPrice = asin.referencePrice * (1 - defaultDiscountRate / 100);
            var maxAllowedFunding = calculateMaxAllowedFunding(asin.referencePrice);
            var minAllowedPrice = calculateMinAllowedPrice(asin.referencePrice);

            // 智能设置默认促销价
            targetDiscountPrice = Math.max(targetDiscountPrice, minAllowedPrice);

            if (asin.maxPrice && asin.maxPrice < targetDiscountPrice) {
                asin.dealPrice = parseFloat(Math.max(asin.maxPrice, minAllowedPrice).toFixed(2));
                asin.perUnitFunding = parseFloat((asin.referencePrice - asin.dealPrice).toFixed(2));
                asin.perUnitFunding = parseFloat(Math.min(asin.perUnitFunding, maxAllowedFunding).toFixed(2));
            } else {
                asin.dealPrice = parseFloat(targetDiscountPrice.toFixed(2));
                if (!asin.maxPrice) {
                    asin.maxPrice = parseFloat(targetDiscountPrice.toFixed(2));
                }
                asin.perUnitFunding = parseFloat((asin.referencePrice - targetDiscountPrice).toFixed(2));
                asin.perUnitFunding = parseFloat(Math.min(asin.perUnitFunding, maxAllowedFunding).toFixed(2));
            }

            // 设置默认承诺数量
            if (!asin.committedUnits) {
                asin.committedUnits = asin.expectedDemand || 1;
            }
        }

        // 获取竞争摘要信息
        function fetchCompetitiveSummary(asinList, shopCode) {
            return new Promise(function(resolve, reject) {
                if (!asinList || asinList.length === 0) {
                    reject(new Error('ASIN列表不能为空'));
                    return;
                }

                if (!shopCode) {
                    reject(new Error('店铺编码不能为空'));
                    return;
                }

                $.ajax({
                    url: toolPrefix + "/batchGetCompetitiveSummary",
                    type: "POST",
                    contentType: "application/json",
                    data: JSON.stringify({
                        asinList: asinList,
                        shopCode: shopCode
                    }),
                    timeout: 30000, // 30秒超时
                    success: function(response) {
                        if (response.code === 0) {
                            resolve(response.data);
                        } else {
                            reject(new Error(response.msg || '获取竞争摘要失败'));
                        }
                    },
                    error: function(xhr, status, error) {
                        if (status === 'timeout') {
                            reject(new Error('请求超时，请稍后重试'));
                        } else {
                            reject(new Error('网络请求失败: ' + error));
                        }
                    }
                });
            });
        }

        // 批量获取多个ASIN的竞争摘要信息
        function batchFetchCompetitiveSummary(asinList) {
            if (!asinList || asinList.length === 0) {
                return Promise.resolve({});
            }

            var shopCode = 'VC1'
            var platformGoodsIds = asinList.map(function(asin) {
                return asin.platformGoodsId;
            });

            return fetchCompetitiveSummary(platformGoodsIds, shopCode)
                .then(function(summaryData) {
                    // 更新ASIN列表中的价格信息
                    for (var i = 0; i < asinList.length; i++) {
                        var asin = asinList[i];
                        var competitiveInfo = summaryData[asin.platformGoodsId];

                        if (competitiveInfo) {
                            if (competitiveInfo.referencePrice) {
                                asin.referencePrice = parseFloat(competitiveInfo.referencePrice);
                            }
                            if (competitiveInfo.maxPrice) {
                                asin.maxPrice = parseFloat(competitiveInfo.maxPrice);
                            }

                            // 重新计算价格
                            var eventType = parseInt($("#form-record-edit select[name='eventType']").val()) || 1;
                            recalculateAsinPricing(asin, eventType);
                        }
                    }

                    return summaryData;
                })
                .catch(function(error) {
                    console.warn('批量获取竞争摘要失败:', error);
                    // 失败时为每个ASIN使用默认逻辑
                    var eventType = parseInt($("#form-record-edit select[name='eventType']").val()) || 1;
                    for (var i = 0; i < asinList.length; i++) {
                        useDefaultPricingLogic(asinList[i], eventType);
                    }
                    return {};
                });
        }

        // 生成模拟参考价
        function generateMockReferencePrice(asin) {
            if (!asin) return 19.99;

            var hash = 0;
            for (var i = 0; i < asin.length; i++) {
                hash = ((hash << 5) - hash + asin.charCodeAt(i)) & 0xffffffff;
            }
            hash = Math.abs(hash);

            var basePrice;
            if (asin.startsWith('B0')) {
                basePrice = 25.0 + (hash % 75);
            } else if (asin.startsWith('B1')) {
                basePrice = 15.0 + (hash % 50);
            } else {
                basePrice = 10.0 + (hash % 40);
            }

            var decimal = (hash % 100) / 100.0;
            var finalPrice = basePrice + decimal;

            return Math.round(finalPrice * 100.0) / 100.0;
        }

        // 生成模拟最高限价
        function generateMockMaxPrice(asin, referencePrice) {
            if (!asin || !referencePrice) return 15.99;

            var hash = 0;
            for (var i = 0; i < asin.length; i++) {
                hash = ((hash << 7) - hash + asin.charCodeAt(i)) & 0xffffffff;
            }
            hash = Math.abs(hash);

            var discountRate = (85 + (hash % 10)) / 100;
            var maxPrice = referencePrice * discountRate;

            return Math.round(maxPrice * 100.0) / 100.0;
        }

        // 根据事件类型计算最小funding
        function calculateMinFundingByEventType(asin, eventType) {
            var referencePrice = parseFloat(asin.referencePrice) || 0;
            var discountRate = getDefaultDiscountRateByEventType(parseInt(eventType) || 1);
            return referencePrice * discountRate / 100;
        }

        // 获取默认折扣比例
        function getDefaultDiscountRateByEventType(eventType) {
            switch (eventType) {
                case 1: return 10; // 自定义日期：10%
                case 2: return 15; // 会员日：15%
                case 3: return 15; // 黑五：15%
                default: return 10; // 默认：10%
            }
        }

        // 计算80%funding上限
        function calculateMaxAllowedFunding(referencePrice) {
            return parseFloat(referencePrice) * 0.8;
        }

        // 计算促销价下限（参考价的20%）
        function calculateMinAllowedPrice(referencePrice) {
            return parseFloat(referencePrice) * 0.2;
        }

        // 更新促销价
        function updatePromotionPrice(input) {
            var asinIndex = $(input).data('asin-index');
            var newPrice = parseFloat($(input).val()) || 0;

            if (asinIndex >= 0 && asinIndex < selectedAsins.length) {
                var asin = selectedAsins[asinIndex];
                var referencePrice = parseFloat(asin.referencePrice) || 0;
                var minFunding = parseFloat(asin.minFunding) || 0;
                var maxPrice = parseFloat(asin.maxPrice) || 0;
                var maxAllowedFunding = calculateMaxAllowedFunding(referencePrice);
                var minAllowedPrice = calculateMinAllowedPrice(referencePrice);

                // 校验促销价不能低于下限
                if (newPrice < minAllowedPrice) {
                    var errorMsg = '促销价不能低于 $' + minAllowedPrice.toFixed(2) + ' (参考价的20%)';
                    showInputError($(input), errorMsg);
                    $(input).val(asin.dealPrice || '');
                    return;
                }

                // 校验促销价不能超过最高限价
                if (newPrice > maxPrice) {
                    var errorMsg = '促销价不能超过最高限价 $' + maxPrice.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.dealPrice || '');
                    return;
                }

                // 根据促销价计算对应的funding
                var calculatedFunding = referencePrice - newPrice;

                // 校验计算出的funding不能低于最小值
                if (calculatedFunding < minFunding) {
                    var maxAllowedPrice = referencePrice - minFunding;
                    var errorMsg = '促销价过高，会导致funding低于最小值。最高可设置促销价：$' + maxAllowedPrice.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.dealPrice || '');
                    return;
                }

                // 校验计算出的funding不能超过80%上限
                if (calculatedFunding > maxAllowedFunding) {
                    var minAllowedPriceForFunding = referencePrice - maxAllowedFunding;
                    var errorMsg = '促销价过低，会导致funding超过80%上限。最低可设置促销价：$' + minAllowedPriceForFunding.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.dealPrice || '');
                    return;
                }

                // 清除错误提示
                clearInputError($(input));

                // 更新数据
                asin.dealPrice = parseFloat(newPrice.toFixed(2));
                asin.perUnitFunding = parseFloat(calculatedFunding.toFixed(2));

                // 同步更新funding输入框
                var fundingInput = $(input).closest('tr').find('.funding-input');
                fundingInput.val(asin.perUnitFunding.toFixed(2));

                // 更新折扣显示
                updateDiscountDisplay(asinIndex);
            }
        }

        // 更新funding
        function updateFunding(input) {
            var asinIndex = $(input).data('asin-index');
            var newFunding = parseFloat($(input).val()) || 0;

            if (asinIndex >= 0 && asinIndex < selectedAsins.length) {
                var asin = selectedAsins[asinIndex];
                var referencePrice = parseFloat(asin.referencePrice) || 0;
                var minFunding = parseFloat(asin.minFunding) || 0;
                var maxAllowedFunding = calculateMaxAllowedFunding(referencePrice);
                var minAllowedPrice = calculateMinAllowedPrice(referencePrice);

                // 校验funding不能低于最小值
                if (newFunding < minFunding) {
                    var errorMsg = 'Funding不能低于 $' + minFunding.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.perUnitFunding || '');
                    return;
                }

                // 校验funding不能超过80%上限
                if (newFunding > maxAllowedFunding) {
                    var errorMsg = 'Funding不能超过 $' + maxAllowedFunding.toFixed(2) + ' (参考价的80%)';
                    showInputError($(input), errorMsg);
                    $(input).val(asin.perUnitFunding || '');
                    return;
                }

                // 根据funding计算对应的促销价
                var calculatedPrice = referencePrice - newFunding;

                // 校验计算出的促销价不能低于下限
                if (calculatedPrice < minAllowedPrice) {
                    var maxAllowedFunding = referencePrice - minAllowedPrice;
                    var errorMsg = 'Funding过高，会导致促销价低于下限。最高可设置funding：$' + maxAllowedFunding.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.perUnitFunding || '');
                    return;
                }

                // 校验计算出的促销价不能超过最高限价
                if (calculatedPrice > asin.maxPrice) {
                    var minAllowedFunding = referencePrice - asin.maxPrice;
                    var errorMsg = 'Funding过低，会导致促销价超过最高限价。最低可设置funding：$' + minAllowedFunding.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.perUnitFunding || '');
                    return;
                }

                // 清除错误提示
                clearInputError($(input));

                // 更新数据
                asin.perUnitFunding = parseFloat(newFunding.toFixed(2));
                asin.dealPrice = parseFloat(calculatedPrice.toFixed(2));

                // 同步更新促销价输入框
                var priceInput = $(input).closest('tr').find('.promotion-price-input');
                priceInput.val(asin.dealPrice.toFixed(2));

                // 更新折扣显示
                updateDiscountDisplay(asinIndex);
            }
        }

        // 更新承诺数量
        function updateCommittedUnits(input) {
            var asinIndex = $(input).data('asin-index');
            var newCommittedUnits = parseInt($(input).val()) || 0;

            if (asinIndex >= 0 && asinIndex < selectedAsins.length) {
                var asin = selectedAsins[asinIndex];
                asin.committedUnits = newCommittedUnits;
            }
        }

        // 更新折扣显示
        function updateDiscountDisplay(asinIndex) {
            if (asinIndex >= 0 && asinIndex < selectedAsins.length) {
                var asin = selectedAsins[asinIndex];
                var referencePrice = parseFloat(asin.referencePrice) || 0;
                var dealPrice = parseFloat(asin.dealPrice) || 0;

                var discountPercent = 0;
                if (referencePrice > 0) {
                    discountPercent = ((referencePrice - dealPrice) / referencePrice) * 100;
                }

                var discountElement = $('[data-asin-index="' + asinIndex + '"].discount-display');
                discountElement.text(discountPercent.toFixed(1) + '%');
            }
        }

        // 显示输入框错误提示
        function showInputError(inputElement, message) {
            clearInputError(inputElement);
            inputElement.addClass('is-invalid');
            var errorDiv = $('<div class="invalid-feedback">' + message + '</div>');
            inputElement.closest('.price-cell').append(errorDiv);
        }

        // 清除输入框错误提示
        function clearInputError(inputElement) {
            inputElement.removeClass('is-invalid');
            inputElement.closest('.price-cell').find('.invalid-feedback').remove();
        }

        // 批量添加选中的ASIN
        function addSelectedAsins() {
            var selections = $('#available-table').bootstrapTable('getSelections');
            if (selections.length === 0) {
                $.modal.alertWarning("请先选择要添加的ASIN");
                return;
            }

            // 过滤出未选择的ASIN
            var newAsins = [];
            for (var i = 0; i < selections.length; i++) {
                var exists = selectedAsins.some(function(item) {
                    return item.id === selections[i].id;
                });
                if (!exists) {
                    newAsins.push(selections[i]);
                }
            }

            if (newAsins.length === 0) {
                $.modal.alertWarning("所选ASIN已经在列表中");
                return;
            }

            // 显示加载提示
            $.modal.loading("正在获取价格信息...");

            // 批量获取价格信息
            batchFetchCompetitiveSummary(newAsins)
                .then(function() {
                    // 添加到已选列表
                    for (var i = 0; i < newAsins.length; i++) {
                        selectedAsins.push(newAsins[i]);
                    }

                    refreshSelectedTable();
                    updateSelectedCount();
                    refreshAvailableTable();
                    $.modal.closeLoading();
                })
                .catch(function(error) {
                    console.error('批量获取价格信息失败:', error);

                    // 即使失败也添加ASIN，使用默认价格
                    for (var i = 0; i < newAsins.length; i++) {
                        selectedAsins.push(newAsins[i]);
                    }

                    refreshSelectedTable();
                    updateSelectedCount();
                    refreshAvailableTable();
                    $.modal.closeLoading();
                    $.modal.alertWarning("获取价格信息失败，已使用默认价格");
                });

            $('#available-table').bootstrapTable('uncheckAll');
        }

        // 批量移除选中的ASIN
        function removeSelectedFromAvailable() {
            var selections = $('#available-table').bootstrapTable('getSelections');
            if (selections.length === 0) {
                $.modal.alertWarning("请先选择要移除的ASIN");
                return;
            }

            for (var i = 0; i < selections.length; i++) {
                removeAsinFromSelected(selections[i].id);
            }

            $('#available-table').bootstrapTable('uncheckAll');
        }

        // 移除选中的ASIN
        function removeSelectedAsins() {
            var selections = $('#selected-table').bootstrapTable('getSelections');
            if (selections.length === 0) {
                $.modal.alertWarning("请先选择要移除的ASIN");
                return;
            }

            for (var i = 0; i < selections.length; i++) {
                removeAsinFromSelected(selections[i].id);
            }
        }

        // 清空所有ASIN
        function clearAllAsins() {
            if (selectedAsins.length === 0) {
                $.modal.alertWarning("没有可清空的ASIN");
                return;
            }

            $.modal.confirm("确认要清空所有已选ASIN吗？", function() {
                selectedAsins = [];
                refreshSelectedTable();
                updateSelectedCount();
                refreshAvailableTable();
            });
        }

        // 从Thymeleaf加载初始数据（编辑模式）
        function loadInitialDataFromThymeleaf() {
            if (!recordData) {
                return;
            }

            // 填充ASIN列表
            if (recordData.asinList && recordData.asinList.length > 0) {
                selectedAsins = [];
                for (var i = 0; i < recordData.asinList.length; i++) {
                    var asin = recordData.asinList[i];
                    selectedAsins.push(asin);
                }

                console.log('已加载ASIN数量:', selectedAsins.length);

                // 异步批量获取价格信息
                if (selectedAsins.length > 0) {
                    $.modal.loading("正在获取价格信息...");
                    batchUpdateAsinPriceInfo(selectedAsins)
                        .then(function() {
                            $.modal.closeLoading();

                            // 初始化已选ASIN表格（在数据加载后）
                            initSelectedTable();
                        })
                        .catch(function(error) {
                            console.warn('批量获取价格信息失败:', error);
                            $.modal.closeLoading();

                            // 初始化已选ASIN表格（在数据加载后）
                            initSelectedTable();
                        });
                }
            }
        }

        // 兼容性函数：加载初始数据（保持向后兼容）
        function loadInitialData() {
            // 如果是Thymeleaf模式，数据已经在页面加载时处理了
            if (isEditMode && recordData) {
                return;
            }

            // 否则尝试从URL参数获取
            var urlParams = new URLSearchParams(window.location.search);
            var recordId = urlParams.get('id');

            if (recordId) {
                // 从后端加载数据（编辑模式）
                loadRecordFromServer(recordId);
            } else {
                console.log('没有记录ID，这是新建模式');
            }
        }





        // 根据状态控制基本信息的编辑权限
        function controlBasicInfoEditPermission(status) {
            var currentIsDraft = (status === 'DRAFT');

            // Thymeleaf已经设置了基本的disabled/readonly属性
            // 这里主要处理动态的样式和交互

            // 如果不是草稿状态，禁用日期选择器
            if (!currentIsDraft) {
                if (window.startLayDate) {
                    // 通过移除click事件来禁用laydate
                    $('#startDateUtc').off('click');
                    $('#endDateUtc').off('click');
                }
            }

            // 添加视觉提示
            if (!currentIsDraft) {
                $('#form-record-edit input[readonly], #form-record-edit select[disabled], #form-record-edit textarea[readonly]')
                    .addClass('form-control-readonly')
                    .css({
                        'background-color': '#f5f5f5',
                        'cursor': 'not-allowed'
                    });
            }
        }

        // 获取状态显示名称
        function getStatusDisplayName(status) {
            switch(status) {
                case 'DRAFT': return '草稿';
                case 'NEEDS_YOUR_ATTENTION': return '需要关注';
                case 'APPROVED': return '已批准';
                case 'CANCELED': return '已取消';
                default: return status;
            }
        }

        // 确保ASIN基本数据完整性（不包含异步价格获取）
        function ensureBasicAsinData(asin) {
            // 确保必要的价格字段存在
            if (!asin.referencePrice || asin.referencePrice <= 0) {
                asin.referencePrice = 19.99; // 临时默认值
            }

            // 设置临时默认maxPrice（后续会被真实数据覆盖）
            if (!asin.maxPrice || asin.maxPrice <= 0) {
                asin.maxPrice = generateMockMaxPrice(asin.platformGoodsId, asin.referencePrice);
            }

            // 计算minFunding
            var eventType = parseInt($("#form-record-edit select[name='eventType']").val()) || 1;
            if (!asin.minFunding) {
                asin.minFunding = calculateMinFundingByEventType(asin, eventType);
            }

            // 确保促销价和funding的合理性
            if (!asin.dealPrice || asin.dealPrice <= 0) {
                asin.dealPrice = asin.referencePrice * 0.9; // 默认10%折扣
            }
            if (!asin.perUnitFunding || asin.perUnitFunding <= 0) {
                asin.perUnitFunding = asin.referencePrice - asin.dealPrice;
            }

            // 确保承诺数量
            if (!asin.committedUnits) {
                asin.committedUnits = asin.expectedDemand || 1;
            }
        }

        // 批量更新ASIN价格信息
        function batchUpdateAsinPriceInfo(needUpdateAsins) {
            if (!needUpdateAsins || needUpdateAsins.length === 0) {
                return Promise.resolve();
            }
            var shopCode = 'VC1'
            var platformGoodsIds = needUpdateAsins.map(function(asin) {
                return asin.platformGoodsId;
            });

            return fetchCompetitiveSummary(platformGoodsIds, shopCode)
                .then(function(summaryData) {
                    var updatedCount = 0;

                    for (var i = 0; i < needUpdateAsins.length; i++) {
                        var asin = needUpdateAsins[i];
                        var competitiveInfo = summaryData[asin.platformGoodsId];

                        if (competitiveInfo) {
                            asin.referencePrice = parseFloat(competitiveInfo.referencePrice);
                            asin.maxPrice = parseFloat(competitiveInfo.maxPrice);

                            // 重新计算相关价格
                            var eventType = parseInt($("#form-record-edit select[name='eventType']").val()) || 1;
                            asin.minFunding = calculateMinFundingByEventType(asin, eventType);

                            // 如果促销价不合理，重新计算
                            if (asin.dealPrice > asin.maxPrice || asin.dealPrice < asin.referencePrice * 0.2) {
                                recalculateAsinPricing(asin, eventType);
                            }
                            updatedCount++;
                        }else {
                            asin.referencePrice = null;
                            asin.maxPrice = null;
                            asin.minFunding = null;
                            asin.promotionPrice = null;
                            asin.funding = null;
                            asin.committedUnits = null;
                            updatedCount++;
                        }
                    }

                    return summaryData;
                })
                .catch(function(error) {
                    console.warn('批量获取价格信息失败:', error);
                    // 失败时保持现有数据不变
                    throw error;
                });
        }

        // 弹窗确定按钮回调函数（标准接口）
        function submitHandler(index, layero) {
            // 获取当前记录状态
            var currentStatus = getCurrentRecordStatus();
            var isDraft = (currentStatus === 'DRAFT');

            // 对于草稿状态，验证基本信息表单
            if (isDraft) {
                if (!$("#form-record-edit").validate().form()) {
                    return false;
                }
            }

            if (selectedAsins.length === 0) {
                $.modal.alertWarning("请至少选择一个ASIN");
                return false;
            }

            // 验证ASIN价格数据的完整性
            var priceValidationError = validateAsinPrices();
            if (priceValidationError) {
                $.modal.alertError(priceValidationError);
                return false;
            }

            // 构建提交数据
            var formData = $("#form-record-edit").serializeArray();
            var submitData = {};

            // 转换表单数据
            $.each(formData, function(i, field) {
                submitData[field.name] = field.value;
            });

            // 手动添加disabled字段的值
            submitData.site = $("#form-record-edit select[name='site']").val() || 'US';

            // 确保eventType是数字类型
            submitData.eventType = parseInt(submitData.eventType) || 1;

            // 添加ASIN列表
            submitData.asinList = selectedAsins;

            console.log('准备传递的数据:', submitData);

            // 将数据存储到window对象，供父页面获取
            window.dealData = submitData;

            return true;
        }

        // 获取当前记录状态
        function getCurrentRecordStatus() {
            // 从隐藏字段或全局变量获取状态
            return window.currentRecordStatus || 'DRAFT';
        }

        // 验证ASIN价格数据
        function validateAsinPrices() {
            for (var i = 0; i < selectedAsins.length; i++) {
                var asin = selectedAsins[i];

                // 检查必要的价格字段
                if (!asin.dealPrice || asin.dealPrice <= 0) {
                    return '第' + (i + 1) + '个ASIN的促销价不能为空或小于等于0';
                }

                if (!asin.perUnitFunding || asin.perUnitFunding <= 0) {
                    return '第' + (i + 1) + '个ASIN的funding不能为空或小于等于0';
                }

                if (!asin.committedUnits || asin.committedUnits <= 0) {
                    return '第' + (i + 1) + '个ASIN的承诺数量不能为空或小于等于0';
                }

                // 检查价格范围
                var referencePrice = parseFloat(asin.referencePrice) || 0;
                var dealPrice = parseFloat(asin.dealPrice) || 0;
                var perUnitFunding = parseFloat(asin.perUnitFunding) || 0;

                if (referencePrice > 0) {
                    var minAllowedPrice = calculateMinAllowedPrice(referencePrice);
                    var maxAllowedFunding = calculateMaxAllowedFunding(referencePrice);

                    if (dealPrice < minAllowedPrice) {
                        return '第' + (i + 1) + '个ASIN的促销价不能低于参考价的20%（$' + minAllowedPrice.toFixed(2) + '）';
                    }

                    if (perUnitFunding > maxAllowedFunding) {
                        return '第' + (i + 1) + '个ASIN的funding不能超过参考价的80%（$' + maxAllowedFunding.toFixed(2) + '）';
                    }
                }
            }

            return null; // 验证通过
        }

        // 回车搜索
        $(document).ready(function() {
            $("#available-search").keypress(function(e) {
                if (e.which === 13) {
                    searchAvailableAsins();
                }
            });
        });
    </script>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
</body>
</html>
