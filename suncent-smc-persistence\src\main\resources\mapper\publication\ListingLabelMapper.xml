<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.publication.mapper.ListingLabelMapper">

    <resultMap type="ListingLabel" id="ListingLabelResult">
        <result property="id"    column="id"    />
        <result property="platform"    column="platform"    />
        <result property="siteCode"    column="site_code"    />
        <result property="shopCode"    column="shop_code"    />
        <result property="headId"    column="head_id"    />
        <result property="labelType"    column="label_type"    />
        <result property="label"    column="label"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectListingLabelVo">
        select id, platform, site_code, shop_code, head_id, label_type, label, create_time from sc_smc_listing_label
    </sql>

    <select id="selectListingLabelList" parameterType="ListingLabel" resultMap="ListingLabelResult">
        <include refid="selectListingLabelVo"/>
        <where>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="siteCode != null  and siteCode != ''"> and site_code = #{siteCode}</if>
            <if test="shopCode != null  and shopCode != ''"> and shop_code = #{shopCode}</if>
            <if test="headId != null "> and head_id = #{headId}</if>
            <if test="labelType != null  and labelType != ''"> and label_type = #{labelType}</if>
            <if test="label != null  and label != ''"> and label = #{label}</if>
        </where>
    </select>

    <select id="selectListingLabelById" parameterType="Long" resultMap="ListingLabelResult">
        <include refid="selectListingLabelVo"/>
        where id = #{id}
    </select>
    <select id="selectHeadIdByListingPerformance" resultMap="ListingLabelResult">
        <include refid="selectListingLabelVo"/>
        <where>
            <if test="headIdList != null and headIdList.size() > 0">and head_id in
                <foreach item="headId" collection="headIdList" open="(" separator="," close=")">
                    #{headId}
                </foreach>
            </if>
            <if test="listingPerformance != null and listingPerformance != ''">and label in
                <foreach item="label" collection="listingPerformance.split(',')" open="(" separator="," close=")">
                    #{label}
                </foreach>
            </if>
            <if test="shopCodeList != null and shopCodeList.size() > 0">and shop_code in
                <foreach item="shopCode" collection="shopCodeList" open="(" separator="," close=")">
                    #{shopCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectCartListingLabelList" resultMap="ListingLabelResult">
        <include refid="selectListingLabelVo"/>
        where  label_type ='ads' and label ='有购物车'and shop_code = #{shopCode}
    </select>
    <select id="selectListingLabelByHeadIdAndLabelType" resultMap="ListingLabelResult">
        <include refid="selectListingLabelVo"/>
        where head_id = #{headId} and label_type = #{labelType} limit 1
    </select>

    <insert id="insertListingLabel" parameterType="ListingLabel" useGeneratedKeys="true" keyProperty="id">
        insert into sc_smc_listing_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="headId != null">head_id,</if>
            <if test="platform != null">platform,</if>
            <if test="siteCode != null">site_code,</if>
            <if test="shopCode != null">shop_code,</if>
            <if test="labelType != null">label_type,</if>
            <if test="label != null">label,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="headId != null">#{headId},</if>
            <if test="platform != null">#{platform},</if>
            <if test="siteCode != null">#{siteCode},</if>
            <if test="shopCode != null">#{shopCode},</if>
            <if test="labelType != null">#{labelType},</if>
            <if test="label != null">#{label},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <insert id="insertListingLabelBatch">
        insert into sc_smc_listing_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            head_id,
            platform,
            site_code,
            shop_code,
            label_type,
            label,
            create_time
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.headId},
            #{item.platform},
            #{item.siteCode},
            #{item.shopCode},
            #{item.labelType},
            #{item.label},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <update id="updateListingLabel" parameterType="ListingLabel">
        update sc_smc_listing_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="headId != null">head_id = #{headId},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="siteCode != null">site_code = #{siteCode},</if>
            <if test="shopCode != null">shop_code = #{shopCode},</if>
            <if test="labelType != null">label_type = #{labelType},</if>
            <if test="label != null">label = #{label},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteListingLabelById" parameterType="Long">
        delete from sc_smc_listing_label where id = #{id}
    </delete>

    <delete id="deleteListingLabelByIds" parameterType="String">
        delete from sc_smc_listing_label where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteListingLabelByShopCode">
        delete
        from sc_smc_listing_label
        where shop_code = #{shopCode}
          and label_type = #{labelType}
    </delete>

    <delete id="deleteListingLabelsByShopCode">
        delete
        from sc_smc_listing_label
        where shop_code = #{shopCode}
        and label in
        <foreach item="label" collection="label" open="(" separator="," close=")">
            #{label}
        </foreach>
    </delete>

    <delete id="deleteListingLabelsByHeadIdsAndLabelType">
        delete
        from sc_smc_listing_label
        where shop_code = #{shopCode}
        and label_type = #{labelType}
        and head_id in
        <foreach item="headId" collection="headIds" open="(" separator="," close=")">
            #{headId}
        </foreach>
    </delete>

    <delete id="delYesterdayLabel">
        delete
        from sc_smc_listing_label
        where label_type = #{labelType}
          and DATE (create_time)
         &lt; CURDATE()
    </delete>
    <delete id="deleteListingLabelByLabelTypeAndLabel">
        delete
        from sc_smc_listing_label
        where label_type = #{labelType}
          and label = #{label}
    </delete>
    <delete id="deleteListingLabelByHeadIdAndLabel">
        delete
        from sc_smc_listing_label
        where head_id = #{headId}
          and label = #{label}
    </delete>
    <delete id="deleteListingLabelsByHeadIds">
        delete
        from sc_smc_listing_label
        where head_id in
        <foreach item="headId" collection="headIds" open="(" separator="," close=")">
            #{headId}
        </foreach>
        and label in
        <foreach item="label" collection="labelList" open="(" separator="," close=")">
            #{label}
        </foreach>
    </delete>

    <select id="selectCountByHeadIdAndLabel" resultType="java.lang.Integer">
        select count(1) from sc_smc_listing_label where head_id = #{headId} and label = #{label}
    </select>

    <delete id="deleteListingLabelsByHeadIdsAndLabelTypeExcluding">
        delete from sc_smc_listing_label
        where head_id in
        <foreach item="headId" collection="headIds" open="(" separator="," close=")">
            #{headId}
        </foreach>
        and label_type = #{labelType}
        <if test="shopCode != null and shopCode != ''">
            and shop_code = #{shopCode}
        </if>
        <if test="excludeLabels != null and excludeLabels.size() > 0">
            and label not in
            <foreach item="label" collection="excludeLabels" open="(" separator="," close=")">
                #{label}
            </foreach>
        </if>
    </delete>
</mapper>