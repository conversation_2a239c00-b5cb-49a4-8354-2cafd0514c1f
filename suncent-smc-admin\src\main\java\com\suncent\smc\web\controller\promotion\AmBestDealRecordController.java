package com.suncent.smc.web.controller.promotion;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.suncent.smc.common.annotation.Log;
import com.suncent.smc.common.core.controller.BaseController;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.page.TableDataInfo;
import com.suncent.smc.common.enums.BusinessType;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.poi.ExcelUtil;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import com.suncent.smc.persistence.promotion.domain.entity.AmPromotionLog;
import com.suncent.smc.persistence.promotion.service.IAmBestDealRecordService;
import com.suncent.smc.persistence.promotion.service.IAmPromotionLogService;
import com.suncent.smc.web.controller.promotion.dto.StatisticsExportDTO;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * BD促销记录Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Controller
@RequestMapping("/promotion/record")
public class AmBestDealRecordController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(AmBestDealRecordController.class);

    private String prefix = "promotion/record";

    @Autowired
    private IAmBestDealRecordService amBestDealRecordService;

    @Autowired
    private IAmPromotionLogService promotionLogService;

    @RequiresPermissions("promotion:record:view")
    @GetMapping()
    public String record() {
        return prefix + "/list";
    }

    /**
     * 查询BD促销记录列表
     */
    @RequiresPermissions("promotion:record:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AmBestDealRecord amBestDealRecord,
                             @RequestParam(value = "startDateFrom", required = false) String startDateFrom,
                             @RequestParam(value = "startDateTo", required = false) String startDateTo,
                             @RequestParam(value = "endDateFrom", required = false) String endDateFrom,
                             @RequestParam(value = "endDateTo", required = false) String endDateTo,
                             @RequestParam(value = "keyword", required = false) String keyword) {
        try {
            startPage();

            // 设置日期范围查询条件
            if (StrUtil.isNotBlank(startDateFrom) || StrUtil.isNotBlank(startDateTo)) {
                Map<String, Object> params = amBestDealRecord.getParams();
                if (params == null) {
                    params = new HashMap<>();
                    amBestDealRecord.setParams(params);
                }
                if (StrUtil.isNotBlank(startDateFrom)) {
                    params.put("startDateFrom", startDateFrom);
                }
                if (StrUtil.isNotBlank(startDateTo)) {
                    params.put("startDateTo", startDateTo);
                }
            }

            if (StrUtil.isNotBlank(endDateFrom) || StrUtil.isNotBlank(endDateTo)) {
                Map<String, Object> params = amBestDealRecord.getParams();
                if (params == null) {
                    params = new HashMap<>();
                    amBestDealRecord.setParams(params);
                }
                if (StrUtil.isNotBlank(endDateFrom)) {
                    params.put("endDateFrom", endDateFrom);
                }
                if (StrUtil.isNotBlank(endDateTo)) {
                    params.put("endDateTo", endDateTo);
                }
            }

            // 设置关键字搜索条件
            if (StrUtil.isNotBlank(keyword)) {
                Map<String, Object> params = amBestDealRecord.getParams();
                if (params == null) {
                    params = new HashMap<>();
                    amBestDealRecord.setParams(params);
                }
                params.put("keyword", keyword);
            }

            List<AmBestDealRecord> list = amBestDealRecordService.selectAmBestDealRecordListWithAsinData(amBestDealRecord);
            return getDataTable(list);

        } catch (Exception e) {
            logger.error("查询BD促销记录列表失败", e);
            return getDataTable(java.util.Collections.emptyList());
        }
    }

    /**
     * 高级搜索BD促销记录
     */
    @RequiresPermissions("promotion:record:list")
    @PostMapping("/advancedSearch")
    @ResponseBody
    public TableDataInfo advancedSearch(@RequestBody Map<String, Object> searchParams) {
        try {
            startPage();

            AmBestDealRecord searchRecord = new AmBestDealRecord();

            // 基本字段搜索
            if (searchParams.containsKey("site")) {
                searchRecord.setSite((String) searchParams.get("site"));
            }
            if (searchParams.containsKey("status")) {
                searchRecord.setStatus((String) searchParams.get("status"));
            }
            if (searchParams.containsKey("publishType")) {
                searchRecord.setPublishType((Integer) searchParams.get("publishType"));
            }
            if (searchParams.containsKey("dealType")) {
                searchRecord.setDealType((Integer) searchParams.get("dealType"));
            }

            // 设置扩展搜索参数
            Map<String, Object> params = new HashMap<>();
            for (String key : searchParams.keySet()) {
                if (searchParams.get(key) != null && !searchParams.get(key).toString().isEmpty()) {
                    params.put(key, searchParams.get(key));
                }
            }
            searchRecord.setParams(params);

            List<AmBestDealRecord> list = amBestDealRecordService.selectAmBestDealRecordList(searchRecord);
            return getDataTable(list);

        } catch (Exception e) {
            logger.error("高级搜索BD促销记录失败", e);
            return getDataTable(java.util.Collections.emptyList());
        }
    }

    /**
     * 按站点统计BD促销记录
     */
    @RequiresPermissions("promotion:record:view")
    @PostMapping("/statsBySite")
    @ResponseBody
    public AjaxResult statsBySite() {
        try {
            Map<String, Object> stats = amBestDealRecordService.getStatsBySite();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("按站点统计BD促销记录失败", e);
            return AjaxResult.error("统计失败：" + e.getMessage());
        }
    }

    /**
     * 按状态统计BD促销记录
     */
    @RequiresPermissions("promotion:record:view")
    @PostMapping("/statsByStatus")
    @ResponseBody
    public AjaxResult statsByStatus() {
        try {
            Map<String, Object> stats = amBestDealRecordService.getStatsByStatus();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("按状态统计BD促销记录失败", e);
            return AjaxResult.error("统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取BD促销记录总体统计
     */
    @RequiresPermissions("promotion:record:view")
    @PostMapping("/overallStats")
    @ResponseBody
    public AjaxResult overallStats() {
        try {
            Map<String, Object> stats = amBestDealRecordService.getOverallStats();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取BD促销记录总体统计失败", e);
            return AjaxResult.error("统计失败：" + e.getMessage());
        }
    }

    /**
     * 导出BD促销记录列表
     */
    @RequiresPermissions("promotion:record:export")
    @Log(title = "BD促销记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AmBestDealRecord amBestDealRecord) {
        List<AmBestDealRecord> list = amBestDealRecordService.selectAmBestDealRecordList(amBestDealRecord);
        ExcelUtil<AmBestDealRecord> util = new ExcelUtil<AmBestDealRecord>(AmBestDealRecord.class);
        return util.exportExcel(list, "BD促销记录数据");
    }

    /**
     * 新增BD促销记录
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 批量新增BD促销记录
     */
    @RequiresPermissions("promotion:record:add")
    @Log(title = "BD促销记录批量新增", businessType = BusinessType.INSERT)
    @PostMapping("/addBatch")
    @ResponseBody
    public AjaxResult addBatch(@RequestBody List<AmBestDealRecord> records) {
        try {
            // 参数校验
            if (records == null || records.isEmpty()) {
                return AjaxResult.error("批量数据不能为空");
            }

            // 数量限制校验
            if (records.size() > 100) {
                return AjaxResult.error("单次批量创建不能超过100条记录");
            }

            // 数据校验
            for (int i = 0; i < records.size(); i++) {
                AmBestDealRecord record = records.get(i);
                record.setCreateBy(ShiroUtils.getUserId()+"");
                String validationError = validateBestDealRecord(record, i + 1);
                if (validationError != null) {
                    return AjaxResult.error(validationError);
                }
            }

            // 执行批量创建
            int result = amBestDealRecordService.batchInsertAmBestDealRecord(records);

            if (result > 0) {
                return AjaxResult.success(String.format("成功创建 %d 条BD促销记录", result));
            } else {
                return AjaxResult.error("批量创建失败");
            }

        } catch (Exception e) {
            logger.error("批量创建BD促销记录失败", e);
            return AjaxResult.error("批量创建失败：" + e.getMessage());
        }
    }

    /**
     * 获取批量创建模态框页面
     * 数据通过前端sessionStorage传递，后端只需返回页面
     */
    @GetMapping("/addModal")
    public String addModal() {
        return prefix + "/addModal";
    }

    /**
     * 获取编辑模态框页面
     * 支持新建模式和编辑模式
     */
    @GetMapping("/editModal")
    public String editModal(@RequestParam(value = "id", required = false) Long id, ModelMap mmap) {
        if (id != null) {
            // 编辑模式：获取记录数据
            try {
                AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordById(id);
                if (record != null) {
                    mmap.put("record", record);
                    mmap.put("isEditMode", true);

                    // 根据状态设置编辑权限
                    boolean isDraft = "DRAFT".equals(record.getStatus());
                    mmap.put("isDraft", isDraft);

                    // 设置页面标题
                    String statusText = getStatusDisplayName(record.getStatus());
                    String pageTitle = isDraft ? "编辑BD促销" : "编辑BD促销 - " + statusText + "（仅可编辑ASIN）";
                    mmap.put("pageTitle", pageTitle);
                } else {
                    logger.warn("未找到ID为{}的促销记录", id);
                    mmap.put("error", "未找到指定的促销记录");
                }
            } catch (Exception e) {
                logger.error("获取促销记录失败，ID: {}", id, e);
                mmap.put("error", "获取促销记录失败：" + e.getMessage());
            }
        } else {
            // 新建模式
            mmap.put("isEditMode", false);
            mmap.put("isDraft", true);
            mmap.put("pageTitle", "新建BD促销");
        }

        return prefix + "/editModal";
    }

    /**
     * 获取详情查看页面
     */
    @RequiresPermissions("promotion:record:view")
    @GetMapping("/viewModal")
    public String viewModal(@RequestParam(value = "id", required = true) Long id, ModelMap mmap) {
        try {
            AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordById(id);
            if (record != null) {
                mmap.put("record", record);
                mmap.put("isViewMode", true);

                // 设置页面标题
                String statusText = getStatusDisplayName(record.getStatus());
                String pageTitle = "查看BD促销详情 - " + statusText;
                mmap.put("pageTitle", pageTitle);
            } else {
                logger.warn("未找到ID为{}的促销记录", id);
                mmap.put("error", "未找到指定的促销记录");
            }
        } catch (Exception e) {
            logger.error("获取促销记录详情失败，ID: {}", id, e);
            mmap.put("error", "获取促销记录详情失败：" + e.getMessage());
        }

        return prefix + "/viewModal";
    }

    /**
     * 获取所有不重复的状态值
     */
    @RequiresPermissions("promotion:record:list")
    @PostMapping("/getStatusList")
    @ResponseBody
    public AjaxResult getStatusList() {
        try {
            List<String> statusList = amBestDealRecordService.getDistinctStatusList();
            return AjaxResult.success(statusList);
        } catch (Exception e) {
            logger.error("获取状态列表失败", e);
            return AjaxResult.error("获取状态列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取ASIN选择器页面
     */
    @GetMapping("/asinSelector")
    public String asinSelector() {
        return prefix + "/asinSelector";
    }

    /**
     * 获取统计分析页面
     */
    @GetMapping("/statistics")
    public String statistics() {
        return prefix + "/statistics";
    }

    /**
     * 导出统计数据
     */
    @RequiresPermissions("promotion:record:export")
    @PostMapping("/exportStatistics")
    @ResponseBody
    public AjaxResult exportStatistics() {
        try {
            // 获取统计数据
            Map<String, Object> overallStats = amBestDealRecordService.getOverallStats();

            // 构建导出数据 - 使用专门的实体类
            List<StatisticsExportDTO> exportData = new ArrayList<>();
            String currentTime = com.suncent.smc.common.utils.DateUtils.getTime();

            // 添加总体统计
            StatisticsExportDTO totalStats = StatisticsExportDTO.createOverallStatistics(
                "总记录数", String.valueOf(overallStats.get("totalCount")));
            totalStats.setStatisticsTime(currentTime);
            exportData.add(totalStats);

            // 添加ASIN总数统计
            StatisticsExportDTO asinStats = StatisticsExportDTO.createOverallStatistics(
                "总ASIN数", String.valueOf(overallStats.get("totalAsinCount")));
            asinStats.setStatisticsTime(currentTime);
            exportData.add(asinStats);

            // 添加状态统计
            List<Map<String, Object>> statusStats = (List<Map<String, Object>>) overallStats.get("statusStats");
            if (statusStats != null) {
                for (Map<String, Object> stat : statusStats) {
                    String status = getStatusDisplayName((String) stat.get("status"));
                    Integer count = Convert.toInt(stat.get("count"));
                    String percentage = String.format("%.1f",
                        count * 100.0 / Convert.toInt(overallStats.get("totalCount")));

                    StatisticsExportDTO statusDto = StatisticsExportDTO.createStatusStatistics(
                        status, count, 0, percentage);
                    statusDto.setStatisticsTime(currentTime);
                    exportData.add(statusDto);
                }
            }

            // 添加站点统计
            List<Map<String, Object>> siteStats = (List<Map<String, Object>>) overallStats.get("siteStats");
            if (siteStats != null) {
                for (Map<String, Object> stat : siteStats) {
                    String site = stat.get("site") + "站";
                    Integer count = Convert.toInt(stat.get("count"));
                    String percentage = String.format("%.1f",
                        count * 100.0 / Convert.toInt(overallStats.get("totalCount")));

                    StatisticsExportDTO siteDto = StatisticsExportDTO.createSiteStatistics(
                        site, count, 0, percentage);
                    siteDto.setStatisticsTime(currentTime);
                    exportData.add(siteDto);
                }
            }

            // 使用ExcelUtil导出 - 使用专门的实体类
            ExcelUtil<StatisticsExportDTO> util = new ExcelUtil<>(StatisticsExportDTO.class);
            return util.exportExcel(exportData, "BD促销统计数据");

        } catch (Exception e) {
            logger.error("导出统计数据失败", e);
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取状态显示名称
     */
    private String getStatusDisplayName(String status) {
        switch (status) {
            case "DRAFT": return "草稿";
            case "NEEDS_YOUR_ATTENTION": return "需要关注";
            case "APPROVED": return "已批准";
            case "CANCELED": return "已取消";
            default: return status;
        }
    }

    /**
     * 修改BD促销记录
     */
    @RequiresPermissions("promotion:record:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        AmBestDealRecord amBestDealRecord = amBestDealRecordService.selectAmBestDealRecordById(id);
        mmap.put("amBestDealRecord", amBestDealRecord);
        return prefix + "/edit";
    }

    /**
     * 修改保存BD促销记录
     */
    @RequiresPermissions("promotion:record:edit")
    @Log(title = "BD促销记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody AmBestDealRecord amBestDealRecord) {
        try {
            // 参数校验
            if (amBestDealRecord.getId() == null) {
                return AjaxResult.error("记录ID不能为空");
            }

            // 检查记录是否存在
            AmBestDealRecord existingRecord = amBestDealRecordService.selectAmBestDealRecordById(amBestDealRecord.getId());
            if (existingRecord == null) {
                return AjaxResult.error("BD促销记录不存在");
            }

            // 数据校验
            String validationError = validateBestDealRecord(amBestDealRecord, 1);
            if (validationError != null) {
                return AjaxResult.error(validationError.replace("第1条记录：", ""));
            }

            // 执行更新
            int result = amBestDealRecordService.updateAmBestDealRecord(amBestDealRecord);

            if (result > 0) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败");
            }

        } catch (Exception e) {
            logger.error("更新BD促销记录失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取BD促销记录详情（JSON格式）
     */
    @RequiresPermissions("promotion:record:view")
    @GetMapping("/detail/{id}")
    @ResponseBody
    public AjaxResult getDetail(@PathVariable("id") Long id) {
        try {
            AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordById(id);
            if (record == null) {
                return AjaxResult.error("BD促销记录不存在");
            }
            return AjaxResult.success(record);
        } catch (Exception e) {
            logger.error("获取BD促销记录详情失败", e);
            return AjaxResult.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新BD促销记录状态
     */
    @RequiresPermissions("promotion:record:edit")
    @Log(title = "BD促销记录状态变更", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    @ResponseBody
    public AjaxResult updateStatus(@RequestParam("id") Long id, @RequestParam("status") String status) {
        try {
            // 状态值校验
            if (!isValidStatus(status)) {
                return AjaxResult.error("无效的状态值");
            }

            int result = amBestDealRecordService.updateAmBestDealRecordStatus(id, status);

            if (result > 0) {
                return AjaxResult.success("状态更新成功");
            } else {
                return AjaxResult.error("状态更新失败");
            }

        } catch (Exception e) {
            logger.error("更新BD促销记录状态失败", e);
            return AjaxResult.error("状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新BD促销记录状态
     */
    @RequiresPermissions("promotion:record:edit")
    @Log(title = "BD促销记录批量状态变更", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateStatus")
    @ResponseBody
    public AjaxResult batchUpdateStatus(@RequestParam("ids") String ids, @RequestParam("status") String status) {
        try {
            // 状态值校验
            if (!isValidStatus(status)) {
                return AjaxResult.error("无效的状态值");
            }

            String[] idArray = Convert.toStrArray(ids);
            if (idArray.length == 0) {
                return AjaxResult.error("请选择要更新的记录");
            }

            if (idArray.length > 50) {
                return AjaxResult.error("单次批量更新不能超过50条记录");
            }

            int successCount = 0;
            int failCount = 0;

            for (String idStr : idArray) {
                try {
                    Long id = Long.parseLong(idStr);
                    int result = amBestDealRecordService.updateAmBestDealRecordStatus(id, status);
                    if (result > 0) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    logger.error("更新记录状态失败，ID: " + idStr, e);
                    failCount++;
                }
            }

            if (failCount == 0) {
                return AjaxResult.success(String.format("成功更新 %d 条记录状态", successCount));
            } else {
                return AjaxResult.warn(String.format("成功更新 %d 条记录，失败 %d 条记录", successCount, failCount));
            }

        } catch (Exception e) {
            logger.error("批量更新BD促销记录状态失败", e);
            return AjaxResult.error("批量状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 校验状态值是否有效
     */
    private boolean isValidStatus(String status) {
        return "DRAFT".equals(status) ||
               "NEEDS_YOUR_ATTENTION".equals(status) ||
               "CANCELED".equals(status) ||
               "APPROVED".equals(status);
    }

    /**
     * 删除BD促销记录
     */
    @RequiresPermissions("promotion:record:remove")
    @Log(title = "BD促销记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(amBestDealRecordService.deleteAmBestDealRecordByIds(ids));
    }

    /**
     * 查看BD促销记录的操作日志
     */
    @RequiresPermissions("promotion:record:view")
    @GetMapping("/logs/{id}")
    public String showLogs(@PathVariable("id") Long id, ModelMap mmap) {
        AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordById(id);
        mmap.put("record", record);
        return prefix + "/logs";
    }

    /**
     * 获取BD促销记录的操作日志列表
     */
    @RequiresPermissions("promotion:record:view")
    @PostMapping("/logs/list/{id}")
    @ResponseBody
    public TableDataInfo logsList(@PathVariable("id") Long id) {
        try {
            startPage();
            List<AmPromotionLog> logs = promotionLogService.selectAmPromotionLogByRefId(id.intValue());
            return getDataTable(logs);
        } catch (Exception e) {
            logger.error("获取BD促销记录操作日志失败", e);
            return getDataTable(java.util.Collections.emptyList());
        }
    }

    /**
     * 导出BD促销记录的操作日志
     */
    @RequiresPermissions("promotion:record:view")
    @PostMapping("/logs/export/{id}")
    @ResponseBody
    public AjaxResult exportLogs(@PathVariable("id") Long id) {
        try {
            List<AmPromotionLog> logs = promotionLogService.selectAmPromotionLogByRefId(id.intValue());
            ExcelUtil<AmPromotionLog> util = new ExcelUtil<AmPromotionLog>(AmPromotionLog.class);
            return util.exportExcel(logs, "BD促销操作日志");
        } catch (Exception e) {
            logger.error("导出BD促销记录操作日志失败", e);
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 查看BD促销记录的综合日志
     */
    @RequiresPermissions("promotion:record:view")
    @GetMapping("/logsIntegrated/{id}")
    public String showIntegratedLogs(@PathVariable("id") Long id, ModelMap mmap) {
        AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordById(id);
        mmap.put("record", record);
        return prefix + "/logsIntegrated";
    }

    /**
     * 导出BD促销记录的所有日志
     */
    @RequiresPermissions("promotion:record:view")
    @PostMapping("/logs/exportAll/{id}")
    @ResponseBody
    public AjaxResult exportAllLogs(@PathVariable("id") Long id) {
        try {
            // 获取操作日志
            List<AmPromotionLog> operationLogs = promotionLogService.selectAmPromotionLogByRefId(id.intValue());

            // 构建综合导出数据
            List<Map<String, Object>> exportData = new ArrayList<>();

            // 添加操作日志
            for (AmPromotionLog log : operationLogs) {
                Map<String, Object> row = new HashMap<>();
                row.put("logType", "操作日志");
                row.put("time", log.getOperTime());
                row.put("operator", log.getOperName());
                row.put("details", log.getDetails());
                row.put("status", log.getStatusName());
                row.put("errorMsg", log.getErrorMsg());
                exportData.add(row);
            }

            // 这里可以添加事件日志的导出
            // TODO: 添加事件日志和ASIN事件的导出

            // 使用ExcelUtil导出 - 修复泛型问题
            ExcelUtil<Map<String, Object>> util = new ExcelUtil<>((Class<Map<String, Object>>) (Class<?>) Map.class);
            return util.exportExcel(exportData, "BD促销综合日志");

        } catch (Exception e) {
            logger.error("导出BD促销记录综合日志失败", e);
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 校验BD促销记录数据
     *
     * @param record BD促销记录
     * @param index 记录索引（用于错误提示）
     * @return 错误信息，null表示校验通过
     */
    private String validateBestDealRecord(AmBestDealRecord record, int index) {
        String prefix = String.format("第%d条记录：", index);

        // 必填字段校验
        if (StrUtil.isBlank(record.getPromotionName())) {
            return prefix + "促销名称不能为空";
        }

        if (StrUtil.isBlank(record.getSite())) {
            return prefix + "站点不能为空";
        }

        if (record.getPublishType() == null) {
            return prefix + "刊登类型不能为空";
        }

        if (record.getPublishType() != 5 && record.getPublishType() != 6) {
            return prefix + "刊登类型必须是VCDF(5)或VCPO(6)";
        }

        if (record.getEventType() == null) {
            return prefix + "事件类型不能为空";
        }

        if (record.getEventType() != 1 && record.getEventType() != 2 && record.getEventType() != 3) {
            return prefix + "事件类型必须是自定义日期(1)、会员日(2)或黑五(3)";
        }

        if (StrUtil.isBlank(record.getStatus())) {
            return prefix + "状态不能为空";
        }

        // 根据事件类型验证时间字段
        if (record.getEventType() == 1) {
            // 自定义日期：必须有开始时间和结束时间
            if (StrUtil.isBlank(record.getStartDateUtc())) {
                return prefix + "自定义日期类型的开始时间不能为空";
            }
            if (StrUtil.isBlank(record.getEndDateUtc())) {
                return prefix + "自定义日期类型的结束时间不能为空";
            }
        } else {
            // 会员日或黑五：时间字段可以为空，后端会自动处理
            // 不进行时间字段的验证
        }

        // ASIN列表校验
        if (CollUtil.isEmpty(record.getAsinList())) {
            return prefix + "ASIN列表不能为空";
        }

        if (record.getAsinList().size() > 50) {
            return prefix + "单个BD促销最多支持50个ASIN";
        }

        // ASIN数据校验
        for (int i = 0; i < record.getAsinList().size(); i++) {
            AmBestDealAsin asin = record.getAsinList().get(i);
            String asinError = validateBestDealAsin(asin, index, i + 1);
            if (asinError != null) {
                return asinError;
            }
        }

        return null;
    }

    /**
     * 校验BD促销ASIN数据
     *
     * @param asin ASIN记录
     * @param recordIndex BD记录索引
     * @param asinIndex ASIN索引
     * @return 错误信息，null表示校验通过
     */
    private String validateBestDealAsin(AmBestDealAsin asin, int recordIndex, int asinIndex) {
        String prefix = String.format("第%d条记录第%d个ASIN：", recordIndex, asinIndex);

        if (StrUtil.isBlank(asin.getPlatformGoodsId())) {
            return prefix + "ASIN不能为空";
        }

        if (asin.getPlatformGoodsId().length() != 10) {
            return prefix + "ASIN格式不正确，应为10位字符";
        }

        if (StrUtil.isBlank(asin.getPlatformGoodsCode())) {
            return prefix + "平台商品编码不能为空";
        }

        if (asin.getReferencePrice() == null || asin.getReferencePrice().compareTo(BigDecimal.ZERO) <= 0) {
            return prefix + "引用价格必须大于0";
        }

        if (asin.getCommittedUnits() == null || asin.getCommittedUnits() <= 0) {
            return prefix + "承诺数量必须大于0";
        }

        if (asin.getLowestDiscount() == null || asin.getLowestDiscount() < 0 || asin.getLowestDiscount() > 100) {
            return prefix + "最低折扣必须在0-100之间";
        }

        if (asin.getActualDiscount() == null || asin.getActualDiscount() < 0 || asin.getActualDiscount() > 100) {
            return prefix + "实际折扣必须在0-100之间";
        }

        if (asin.getDealPrice() == null || asin.getDealPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return prefix + "促销价必须大于0";
        }

        if (asin.getPerUnitFunding() == null || asin.getPerUnitFunding().compareTo(BigDecimal.ZERO) < 0) {
            return prefix + "单位折扣金额不能为负数";
        }

        return null;
    }


}
