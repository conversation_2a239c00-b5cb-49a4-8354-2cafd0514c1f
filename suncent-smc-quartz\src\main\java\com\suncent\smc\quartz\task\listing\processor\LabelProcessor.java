package com.suncent.smc.quartz.task.listing.processor;

import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 标签处理器接口
 * 用于处理不同类型的标签数据同步
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface LabelProcessor {

    /**
     * 获取标签类型
     *
     * @return 标签类型标识
     */
    String getLabelType();

    /**
     * 分批查询源数据
     *
     * @param shopCode 店铺编码
     * @param offset   偏移量
     * @param limit    限制数量
     * @return 源数据列表
     */
    List<? extends Object> querySourceData(String shopCode, int offset, int limit);

    /**
     * 处理源数据，生成标签
     *
     * @param sourceData 源数据
     * @param shopCode   店铺编码
     * @return 标签列表
     */
    List<ListingLabel> processToLabels(List<? extends Object> sourceData, String shopCode);

    /**
     * 获取分批大小（统一为1000）
     *
     * @return 批次大小
     */
    default int getBatchSize() {
        return 1000;
    }

    /**
     * 获取处理器优先级（数字越小优先级越高）
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 是否启用该处理器
     *
     * @return true-启用，false-禁用
     */
    default boolean isEnabled() {
        return true;
    }

    /**
     * 处理器描述
     *
     * @return 描述信息
     */
    default String getDescription() {
        return getLabelType() + " label processor";
    }

    /**
     * 获取删除条件
     * 用于在删除标签时指定特定的标签值条件
     *
     * @return 删除条件集合，如果为空则删除该类型的所有标签
     */
    default Set<String> getDeleteConditions() {
        return Collections.emptySet();
    }

    /**
     * 是否为排除模式
     * 用于确定getDeleteConditions()返回的条件是包含条件还是排除条件
     *
     * @return true-排除模式(NOT IN)，false-包含模式(IN)，默认为false
     */
    default boolean isExcludeMode() {
        return false;
    }
}
