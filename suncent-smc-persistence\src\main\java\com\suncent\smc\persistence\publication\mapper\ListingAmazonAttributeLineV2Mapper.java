package com.suncent.smc.persistence.publication.mapper;

import com.suncent.smc.persistence.publication.domain.entity.ListingAmazonAttributeLineV2;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 亚马逊商品属性信息行Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ListingAmazonAttributeLineV2Mapper 
{
    /**
     * 查询亚马逊商品属性信息行
     * 
     * @param id 亚马逊商品属性信息行主键
     * @return 亚马逊商品属性信息行
     */
    public ListingAmazonAttributeLineV2 selectListingAmazonAttributeLineV2ById(Long id);

    /**
     * 查询亚马逊商品属性信息行列表
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 亚马逊商品属性信息行集合
     */
    public List<ListingAmazonAttributeLineV2> selectListingAmazonAttributeLineV2List(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2);

    /**
     * 新增亚马逊商品属性信息行
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 结果
     */
    public int insertListingAmazonAttributeLineV2(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2);

    /**
     * 修改亚马逊商品属性信息行
     * 
     * @param listingAmazonAttributeLineV2 亚马逊商品属性信息行
     * @return 结果
     */
    public int updateListingAmazonAttributeLineV2(ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2);

    /**
     * 删除亚马逊商品属性信息行
     * 
     * @param id 亚马逊商品属性信息行主键
     * @return 结果
     */
    public int deleteListingAmazonAttributeLineV2ById(Long id);

    /**
     * 批量删除亚马逊商品属性信息行
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteListingAmazonAttributeLineV2ByIds(String[] ids);

    List<ListingAmazonAttributeLineV2> listByGoodsId(Integer headId);

    void deleteByGoodsId(Integer goodsHeadId);

    List<ListingAmazonAttributeLineV2> selectExtendAttrByGoodsId(@Param("goodsId") Integer goodsId);

    List<ListingAmazonAttributeLineV2> selecAttrByGoodsIds(@Param("goodsIds")List<Long> goodsIds);
    String getPn(@Param("goodsId") Integer goodsId);

    ListingAmazonAttributeLineV2 getPnObj(@Param("goodsId") Integer goodsId);

    ListingAmazonAttributeLineV2 getAttrByPropNodePath(@Param("goodsId") Integer goodsId, @Param("propNodePath") String propNodePath);

    void deleteListingAmazonAttributeLineV2ByGoodId(@Param("goodsId") Integer goodsId, @Param("propNodePaths") List<String> propNodePaths, @Param("tableTypes") List<Integer> tableTypes);

    void updateCategoryIdByGoodsId(@Param("goodsId") Integer goodsId, @Param("categoryId") Integer categoryId);

    String getFollowAsin(@Param("goodsId") Integer goodsId);

    List<ListingAmazonAttributeLineV2> getAttrByPropNodePathAndHeadIds(@Param("goodsIds") List<Integer> goodsIds, @Param("propNodePath") String propNodePath);

    void deleteGTINByGoodId(@Param("goodsId") Integer goodsId);

    void deleteListingAmazonAttributeLineV2ByGoodIdAndPathList(@Param("goodsId") Integer goodsId, @Param("deleteAttrNodePathList") List<String> deleteAttrNodePathList);
}
