package com.suncent.smc.persistence.promotion.domain.entity;

import com.suncent.smc.common.annotation.Excel;
import com.suncent.smc.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * BD促销ASIN对象 am_best_deal_asin
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AmBestDealAsin extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 关联BD记录ID */
    @Excel(name = "BD记录ID")
    private Long refBestDealId;

    /** 链接头部ID，用于标识具体的商品链接 */
    @Excel(name = "链接头部ID")
    private Long headId;

    /** 促销ID，亚马逊后台的ID */
    @Excel(name = "促销ID")
    private String promotionId;

    /** ASIN */
    @Excel(name = "ASIN")
    private String platformGoodsId;

    /** 平台商品编码 */
    @Excel(name = "平台商品编码")
    private String platformGoodsCode;

    /** PDM商品编码 */
    @Excel(name = "PDM商品编码")
    private String pdmGoodsCode;

    /** cost price/your price */
    @Excel(name = "标准价格")
    private BigDecimal standardPrice;

    /** 引用价，实际参与计算折扣的价格 */
    @Excel(name = "引用价格")
    private BigDecimal referencePrice;

    /** 预期需求量 */
    @Excel(name = "预期需求量")
    private Integer expectedDemand;

    /** 承诺数量 */
    @Excel(name = "承诺数量")
    private Integer committedUnits;

    /** 最低折扣，百分比 */
    @Excel(name = "最低折扣")
    private Integer lowestDiscount;

    /** 实际折扣，百分比 */
    @Excel(name = "实际折扣")
    private Integer actualDiscount;

    /** 促销价 */
    @Excel(name = "促销价")
    private BigDecimal dealPrice;

    /** 单位折扣金额 */
    @Excel(name = "单位折扣金额")
    private BigDecimal perUnitFunding;

    /** 删除标记,1是，0否 */
    private Integer delFlag;
}
