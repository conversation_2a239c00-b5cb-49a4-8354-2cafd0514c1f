package com.suncent.smc.persistence.promotion.mapper;

import com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 促销RPA任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface AmPromotionRpaTaskMapper {
    /**
     * 查询促销RPA任务
     *
     * @param id 促销RPA任务主键
     * @return 促销RPA任务
     */
    public AmPromotionRpaTask selectAmPromotionRpaTaskById(Long id);

    /**
     * 查询促销RPA任务列表
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskList(AmPromotionRpaTask amPromotionRpaTask);

    /**
     * 新增促销RPA任务
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 结果
     */
    public int insertAmPromotionRpaTask(AmPromotionRpaTask amPromotionRpaTask);

    /**
     * 修改促销RPA任务
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 结果
     */
    public int updateAmPromotionRpaTask(AmPromotionRpaTask amPromotionRpaTask);

    /**
     * 删除促销RPA任务
     *
     * @param id 促销RPA任务主键
     * @return 结果
     */
    public int deleteAmPromotionRpaTaskById(Long id);

    /**
     * 批量删除促销RPA任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAmPromotionRpaTaskByIds(String[] ids);

    /**
     * 根据BD记录ID查询RPA任务列表
     *
     * @param refBestDealId BD记录ID
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByRefId(Long refBestDealId);

    /**
     * 根据促销ID查询RPA任务列表
     *
     * @param promotionId 促销ID
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByPromotionId(String promotionId);

    /**
     * 查询待处理的RPA任务
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectPendingAmPromotionRpaTasks();

    /**
     * 查询处理中的RPA任务
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectProcessingAmPromotionRpaTasks();

    /**
     * 查询已完成的RPA任务（成功或失败）
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectCompletedAmPromotionRpaTasks();

    /**
     * 根据状态查询RPA任务
     *
     * @param status 状态
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByStatus(@Param("status") Integer status);

    /**
     * 根据活动类型查询RPA任务
     *
     * @param activityType 活动类型
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByActivityType(@Param("activityType") Integer activityType);

    /**
     * 根据操作类型查询RPA任务
     *
     * @param operationType 操作类型
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByOperationType(@Param("operationType") String operationType);

    /**
     * 查询需要SMC处理的任务
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectTasksNeedSmcHandle();

    /**
     * 批量更新SMC处理标识
     *
     * @param ids        任务ID集合
     * @param handleFlag 处理标识
     * @return 结果
     */
    public int batchUpdateHandleFlag(@Param("ids") List<Long> ids, @Param("handleFlag") Integer handleFlag);
}
