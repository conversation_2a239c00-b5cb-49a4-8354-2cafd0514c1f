package com.suncent.smc.quartz.task.listing.am;

import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.enums.SMCCommonEnum;
import com.suncent.smc.persistence.amazon.domain.VcListingInventory;
import com.suncent.smc.persistence.amazon.service.IVcListingInventoryService;
import com.suncent.smc.persistence.inventory.domain.VcInventoryRecord;
import com.suncent.smc.persistence.inventory.service.IVcInventoryRecordService;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.publication.service.IListingLogService;
import com.suncent.smc.provider.biz.publication.AmazonApiHttpRequestBiz;
import com.suncent.smc.provider.biz.publication.dto.AmazonVcDfInventorySubmitDTO;
import com.suncent.smc.provider.biz.publication.dto.VcDfConfirmTransactionStatus;
import com.suncent.smc.provider.biz.publication.service.impl.AmazonPlatformListingServiceImpl;
import com.suncent.smc.provider.dingding.domain.ActionCardMsgDto;
import com.suncent.smc.provider.dingding.service.IDingAsyncSendService;
import com.suncent.smc.system.service.ISysConfigService;
import com.suncent.smc.system.service.ISysUserService;
import com.xxl.job.core.handler.annotation.XxlJob;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 扫描 VC库存更新记录表 待处理的数据
 * 请求 com.suncent.smc.provider.biz.publication.AmazonApiHttpRequestBiz#getVcInventoryUpdateResult
 * 获取vc库存更新结果 回写头表数据
 * <p>
 * 库存更新会处理处理失败的数据
 *
 * <AUTHOR>
 */
@lombok.extern.slf4j.Slf4j
@Component
@Slf4j
public class AmazonVcListingInventorySyncTsk {
    @Autowired
    AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;
    @Autowired
    IVcInventoryRecordService vcInventoryRecordService;
    @Autowired
    IGoodsHeadService goodsHeadService;
    @Autowired
    IVcListingInventoryService vcListingInventoryService;
    @Autowired
    IListingLogService listingLogService;
    @Autowired
    private AmazonPlatformListingServiceImpl amazonPlatformListingService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IDingAsyncSendService dingAsyncSendService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysConfigService sysConfigService;


    @XxlJob("vcListingInventorySyncTsk")
    public void vcListingInventorySyncTsk() {
        try {
            doHandle();
        } catch (Exception e) {
            log.error("获取vc-df库存更新结果失败", e);
        }
    }

    /**
     * 检查仓库暂停错误并发送钉钉通知
     * 每天9点半执行，检查当天是否有仓库暂停错误，如果有则发送钉钉通知给 userid 为 2023 的用户
     */
    @XxlJob("vcWarehouseSuspendedErrorNotification")
    public void vcWarehouseSuspendedErrorNotification() {
        try {
            checkAndSendWarehouseSuspendedNotification();
        } catch (Exception e) {
            log.error("检查仓库暂停错误并发送钉钉通知失败", e);
        }
    }

    private void doHandle() {
        List<VcInventoryRecord> vcInventoryRecords = vcInventoryRecordService.selectProcessedList();
        if (ObjUtil.isEmpty(vcInventoryRecords)) {
            return;
        }
        vcInventoryRecords.forEach(vcInventoryRecord -> {
            try {
                getVcInventoryUpdateResult(vcInventoryRecord);
            } catch (Exception e) {
                log.error("获取vc-df库存更新结果失败,vcInventoryRecord:{}", vcInventoryRecord, e);
                vcInventoryRecord.setStatus(SMCCommonEnum.PROCESSING_FAIL.getValue());
                vcInventoryRecord.setErrorMsg(e.getMessage());
                vcInventoryRecordService.updateVcInventoryRecord(vcInventoryRecord);
            }
        });
    }

    private void getVcInventoryUpdateResult(VcInventoryRecord vcInventoryRecord) {
        AmazonVcDfInventorySubmitDTO submitDTO = new AmazonVcDfInventorySubmitDTO();
        submitDTO.setShopCode(vcInventoryRecord.getShopCode());
        submitDTO.setWarehouseId(vcInventoryRecord.getAmWhCode());
        submitDTO.setTransactionId(vcInventoryRecord.getTransactionId());
        AjaxResult result = amazonApiHttpRequestBiz.getVcInventoryUpdateResult(submitDTO);

        if (result.isSuccess()) {
            VcDfConfirmTransactionStatus response = JSON.parseObject(JSON.toJSONString(result.get(AjaxResult.DATA_TAG)), VcDfConfirmTransactionStatus.class);
            amazonPlatformListingService.callbackHandle(response, vcInventoryRecord);

            if ("Success".equalsIgnoreCase(response.getStatus())) {
                successDataHandle(vcInventoryRecord);
                return;
            } else if ("Processing".equalsIgnoreCase(response.getStatus())) {
                vcInventoryRecord.setStatus(SMCCommonEnum.IN_PROCESS.getValue());
                vcInventoryRecordService.updateVcInventoryRecord(vcInventoryRecord);
                return;
            }
            if ("Failure".equalsIgnoreCase(response.getStatus())) {
                failDataHandle(vcInventoryRecord, response);
                return;
            }

        }
        vcInventoryRecord.setStatus(SMCCommonEnum.PROCESSING_FAIL.getValue());
        vcInventoryRecord.setErrorMsg(String.valueOf(result.get(AjaxResult.MSG_TAG)));
        vcInventoryRecordService.updateVcInventoryRecord(vcInventoryRecord);
    }

    /**
     * 失败数据处理
     *
     * @param vcInventoryRecord
     * @param response
     */
    private void failDataHandle(VcInventoryRecord vcInventoryRecord, VcDfConfirmTransactionStatus response) {
        String amWhCode = vcInventoryRecord.getAmWhCode();
        if (ObjUtil.isEmpty(amWhCode)) {
            log.error("vc库存更新记录表,am仓库编码为空，请检查！transactionId:{}", vcInventoryRecord.getTransactionId());
            return;
        }
        String submitData = vcInventoryRecord.getSubmitData();
        if (ObjUtil.isEmpty(submitData)) {
            log.error("vc库存更新记录表,提交数据为空，请检查！transactionId:{}", vcInventoryRecord.getTransactionId());
            return;
        }
        List<VcListingInventory> listingInventoryList = JSON.parseArray(submitData, VcListingInventory.class);
        Map<String, Integer> availableInventoryMap = listingInventoryList.stream().collect(Collectors.toMap(VcListingInventory::getSellerSku, VcListingInventory::getAvailableInventory));

        List<String> platfromSkuList = listingInventoryList.stream().map(VcListingInventory::getSellerSku).collect(Collectors.toList());
        //写入vc-df报告表，再回写头表
        List<VcListingInventory> vcListingInventoryList = vcListingInventoryService.selectVcListingInventoryListByamWhCodeAndSellerSkuList(amWhCode, platfromSkuList);
        List<VcListingInventory> updateList = new ArrayList<>();
        Map<Integer, String> errorGoodsIdMap = new HashMap<>();
        //对失败的进行解析
        List<VcDfConfirmTransactionStatus.ErrorDetails> errors = response.getErrors();
        StringBuilder errorMsg = new StringBuilder();
        if (ObjUtil.isNotEmpty(errors)) {
            for (VcDfConfirmTransactionStatus.ErrorDetails error : errors) {
                String code = error.getCode();
                String message = error.getMessage();
                errorMsg.append(code).append(" ");

                if (ObjUtil.equals(code, VcDfConfirmTransactionStatus.VcDfInventoryUpdateErrorEnum.ITEM_SUPPRESSED.getCode())) {
                    String pattern = "Item\\s(\\w+)\\s";
                    Pattern regex = Pattern.compile(pattern);
                    Matcher matcher = regex.matcher(message);
                    if (matcher.find()) {
                        String asin = matcher.group(1);
                        Integer goodsId = vcListingInventoryList.stream().filter(vcListingInventory -> vcListingInventory.getAsin().equals(asin)).map(VcListingInventory::getGoodsId).findFirst().orElse(null);
                        if (ObjUtil.isEmpty(goodsId)) {
                            log.error("获取vc-df库存数据有误,且提交vc库存更新失败,未找到商品id，请检查！asin:{},错误消息：{}", asin, message);
                        }
                        errorGoodsIdMap.put(goodsId, "亚马逊仓库编码:" + amWhCode + "更新失败！原因:" + message);
                    }
                } else if (ObjUtil.equals(code, VcDfConfirmTransactionStatus.VcDfInventoryUpdateErrorEnum.WAREHOUSE_IS_SUSPENDED.getCode())) {
                    // 处理仓库暂停错误，记录到 Redis 中
                    handleWarehouseSuspendedError(vcInventoryRecord, error);
                }
            }
        }


        for (VcListingInventory inventory : vcListingInventoryList) {
            Integer availableInventory = availableInventoryMap.get(inventory.getSellerSku());
            if (ObjUtil.isEmpty(availableInventory)) {
                log.error("获取vc-df库存数据有误,未找到库存数据，请检查！sellerSku:{}", inventory.getSellerSku());
                continue;
            }
            if (errorGoodsIdMap.containsKey(inventory.getGoodsId())) {
                continue;
            }
            inventory.setAvailableInventory(availableInventory);
            updateList.add(inventory);
        }
        if (ObjUtil.isEmpty(updateList)) {
            vcInventoryRecord.setStatus(SMCCommonEnum.PROCESSING_FAIL.getValue());
            vcInventoryRecord.setErrorMsg(String.valueOf(errorMsg));
            vcInventoryRecordService.updateVcInventoryRecord(vcInventoryRecord);
            return;
        }
        vcListingInventoryService.updateBatch(updateList);
        //标记该数据已处理
        vcInventoryRecord.setStatus(SMCCommonEnum.PROCESSING_COMPLETE.getValue());
        vcInventoryRecordService.updateVcInventoryRecord(vcInventoryRecord);
        writeBackHead(updateList, errorGoodsIdMap);
    }

    /**
     * 成功数据处理
     *
     * @param vcInventoryRecord
     */
    private void successDataHandle(VcInventoryRecord vcInventoryRecord) {
        String amWhCode = vcInventoryRecord.getAmWhCode();
        if (ObjUtil.isEmpty(amWhCode)) {
            log.error("vc库存更新记录表,am仓库编码为空，请检查！transactionId:{}", vcInventoryRecord.getTransactionId());
            return;
        }
        String submitData = vcInventoryRecord.getSubmitData();
        if (ObjUtil.isEmpty(submitData)) {
            log.error("vc库存更新记录表,提交数据为空，请检查！transactionId:{}", vcInventoryRecord.getTransactionId());
            return;
        }
        List<VcListingInventory> listingInventoryList = JSON.parseArray(submitData, VcListingInventory.class);
        Map<String, Integer> availableInventoryMap = listingInventoryList.stream()
                .collect(Collectors.toMap(
                        VcListingInventory::getSellerSku,
                        VcListingInventory::getAvailableInventory,
                        (existing, replacement) -> existing
                ));

        List<String> platfromSkuList = listingInventoryList.stream().map(VcListingInventory::getSellerSku).collect(Collectors.toList());
        //写入vc-df报告表，再回写头表
        List<VcListingInventory> vcListingInventoryList = vcListingInventoryService.selectVcListingInventoryListByamWhCodeAndSellerSkuList(amWhCode, platfromSkuList);
        List<VcListingInventory> updateList = new ArrayList<>();

        for (VcListingInventory inventory : vcListingInventoryList) {
            Integer availableInventory = availableInventoryMap.get(inventory.getSellerSku());
            if (ObjUtil.isEmpty(availableInventory)) {
                log.error("获取vc-df库存数据有误,未找到库存数据，请检查！sellerSku:{}", inventory.getSellerSku());
                continue;
            }
            inventory.setAvailableInventory(availableInventory);
            updateList.add(inventory);
        }
        if (ObjUtil.isEmpty(updateList)) {
            //标记该数据已处理
            vcInventoryRecord.setStatus(SMCCommonEnum.PROCESSING_COMPLETE.getValue());
            vcInventoryRecordService.updateVcInventoryRecord(vcInventoryRecord);
            return;
        }
        vcListingInventoryService.updateBatch(updateList);
        //标记该数据已处理
        vcInventoryRecord.setStatus(SMCCommonEnum.PROCESSING_COMPLETE.getValue());
        vcInventoryRecordService.updateVcInventoryRecord(vcInventoryRecord);
        writeBackHead(updateList, new HashMap<>());
    }

    /**
     * 回写头表库存 && 写入错误日志
     *
     * @param updateList
     */
    private void writeBackHead(List<VcListingInventory> updateList, Map<Integer, String> errorGoodsIdMap) {
        //回写头表库存
        List<String> sellerSkuLit = updateList.stream().map(VcListingInventory::getSellerSku).collect(Collectors.toList());
        goodsHeadService.updateVcInventoryBySellerSku(sellerSkuLit);

        //写入错误日志
        if (ObjUtil.isNotEmpty(errorGoodsIdMap)) {
            errorGoodsIdMap.forEach((goodsId, msg) -> {
                listingLogService.insertErrorListingLog("VC-DF库存更新失败", "-1", goodsId, msg);
            });
        }
    }

    /**
     * 处理仓库暂停错误，记录到 Redis 中
     *
     * @param vcInventoryRecord VC库存记录
     * @param error             错误详情
     */
    private void handleWarehouseSuspendedError(VcInventoryRecord vcInventoryRecord, VcDfConfirmTransactionStatus.ErrorDetails error) {
        try {
            // 构建 Redis key，按日期分组，使用 Set 结构存储仓库编码
            String dateKey = com.suncent.smc.common.utils.DateUtils.parseDateToStr("yyyyMMdd", new Date());
            String redisKey = "vc:warehouse:suspended:" + dateKey;

            // 获取仓库编码
            String amWhCode = vcInventoryRecord.getAmWhCode();
            if (cn.hutool.core.util.ObjUtil.isEmpty(amWhCode)) {
                log.warn("仓库编码为空，跳过记录，transactionId: {}", vcInventoryRecord.getTransactionId());
                return;
            }

            // 获取现有的 Set，如果不存在则创建新的
            Set<String> warehouseCodes = redisService.getCacheSet(redisKey);
            if (warehouseCodes == null) {
                warehouseCodes = new HashSet<>();
            }

            // 添加仓库编码到 Set 中（自动去重）
            warehouseCodes.add(amWhCode);

            // 重新设置 Set 到 Redis
            redisService.setCacheSet(redisKey, warehouseCodes);

            // 设置过期时间为 1 天
            redisService.expire(redisKey, 1L, TimeUnit.DAYS);

            log.info("记录仓库暂停错误到Redis成功，amWhCode: {}, errorMessage: {}",
                    amWhCode, error.getMessage());
        } catch (Exception e) {
            log.error("记录仓库暂停错误到Redis失败，transactionId: {}, amWhCode: {}",
                    vcInventoryRecord.getTransactionId(), vcInventoryRecord.getAmWhCode(), e);
        }
    }

    /**
     * 检查仓库暂停错误并发送钉钉通知
     */
    private void checkAndSendWarehouseSuspendedNotification() {
        // 获取当天的日期
        String dateKey = com.suncent.smc.common.utils.DateUtils.parseDateToStr("yyyyMMdd", new Date());
        String redisKey = "vc:warehouse:suspended:" + dateKey;
        String notifiedKey = "vc:warehouse:suspended:notified:" + dateKey;

        // 检查是否已经发送过通知
        if (redisService.exists(notifiedKey)) {
            log.info("今天({})的VC仓库暂停错误通知已发送，跳过本次执行", dateKey);
            return;
        }

        // 检查是否存在仓库暂停错误数据
        if (!redisService.exists(redisKey)) {
            log.info("今天({})没有VC仓库暂停错误，无需发送通知", dateKey);
            // 标记为已处理，避免重复检查
            redisService.setCacheObject(notifiedKey, "0", 1L, TimeUnit.DAYS);
            return;
        }

        // 获取仓库暂停错误的仓库编码集合
        Set<String> suspendedWarehouses = redisService.getCacheSet(redisKey);
        if (cn.hutool.core.collection.CollUtil.isEmpty(suspendedWarehouses)) {
            log.info("今天({})的VC仓库暂停错误数据为空，无需发送通知", dateKey);
            redisService.setCacheObject(notifiedKey, "0", 1L, TimeUnit.DAYS);
            return;
        }

        // 发送钉钉通知
        sendDingTalkNotificationToUser2023(suspendedWarehouses, dateKey);

        // 标记已通知
        redisService.setCacheObject(notifiedKey, "1", 1L, TimeUnit.DAYS);

        log.info("VC仓库暂停错误钉钉通知发送完成，日期: {}, 暂停仓库数: {}", dateKey, suspendedWarehouses.size());
    }

    /**
     * 发送钉钉通知给配置的用户
     *
     * @param suspendedWarehouses 暂停的仓库编码集合
     * @param dateKey             日期key
     */
    private void sendDingTalkNotificationToUser2023(Set<String> suspendedWarehouses, String dateKey) {
        try {
            // 从系统配置中获取通知用户ID（支持多个用户，用逗号或空格分隔）
            String configUserIds = sysConfigService.selectConfigByKey("vc.warehouse.suspended.notification.userid");
            if (cn.hutool.core.util.ObjUtil.isEmpty(configUserIds)) {
                log.error("未配置VC仓库暂停错误通知用户ID，请在系统配置中设置 vc.warehouse.suspended.notification.userid");
                return;
            }

            // 解析用户ID列表（支持逗号和空格分隔）
            String[] userIdArray = configUserIds.split("[,\\s]+");
            List<SysUser> targetUsers = new ArrayList<>();
            List<String> invalidUserIds = new ArrayList<>();

            for (String userIdStr : userIdArray) {
                if (cn.hutool.core.util.ObjUtil.isEmpty(userIdStr.trim())) {
                    continue;
                }

                try {
                    Long userId = Long.parseLong(userIdStr.trim());
                    SysUser user = sysUserService.selectUserById(userId);

                    if (user == null) {
                        invalidUserIds.add(userIdStr + "(用户不存在)");
                        continue;
                    }

                    if (cn.hutool.core.util.ObjUtil.isEmpty(user.getUserCode())) {
                        invalidUserIds.add(userIdStr + "(钉钉编码未配置)");
                        continue;
                    }

                    targetUsers.add(user);
                } catch (NumberFormatException e) {
                    invalidUserIds.add(userIdStr + "(格式错误)");
                }
            }

            if (targetUsers.isEmpty()) {
                log.error("没有找到有效的通知用户，配置值: {}, 无效用户: {}", configUserIds, invalidUserIds);
                return;
            }

            if (!invalidUserIds.isEmpty()) {
                log.warn("部分用户配置无效: {}", invalidUserIds);
            }

            // 构建通知内容
            StringBuilder content = new StringBuilder();
            content.append("# VC仓库暂停错误通知\n\n");
            content.append("**日期：** ").append(dateKey).append("\n\n");
            content.append("**暂停的仓库数量：** ").append(suspendedWarehouses.size()).append("\n\n");
            content.append("**暂停的仓库列表：**\n");

            // 列出所有暂停的仓库
            for (String warehouse : suspendedWarehouses) {
                content.append("- ").append(warehouse).append("\n");
            }

            content.append("\n**错误信息：** Warehouse is suspended\n");
            content.append("**处理建议：** 请及时联系亚马逊重新激活暂停的仓库。");

            // 给所有有效用户发送钉钉消息
            int successCount = 0;
            List<String> successUsers = new ArrayList<>();
            List<String> failedUsers = new ArrayList<>();

            for (SysUser user : targetUsers) {
                try {
                    ActionCardMsgDto actionCardMsgDto = new ActionCardMsgDto();
                    actionCardMsgDto.setTargetDingUserId(user.getUserCode());
                    actionCardMsgDto.setMessageTitle("VC仓库暂停错误通知");
                    actionCardMsgDto.setMessageContent(content.toString());
                    actionCardMsgDto.setMessageUrl("");

                    dingAsyncSendService.asyncSend(actionCardMsgDto);

                    successCount++;
                    successUsers.add(user.getUserName() + "(ID:" + user.getUserId() + ")");

                    log.info("VC仓库暂停错误钉钉通知发送成功，接收人: {}(ID:{})", user.getUserName(), user.getUserId());
                } catch (Exception e) {
                    failedUsers.add(user.getUserName() + "(ID:" + user.getUserId() + ")");
                    log.error("发送钉钉通知失败，用户: {}(ID:{})", user.getUserName(), user.getUserId(), e);
                }
            }

            log.info("VC仓库暂停错误钉钉通知批量发送完成，成功: {}/{}, 暂停仓库数: {}, 成功用户: {}",
                    successCount, targetUsers.size(), suspendedWarehouses.size(), successUsers);

            if (!failedUsers.isEmpty()) {
                log.warn("部分用户通知发送失败: {}", failedUsers);
            }

        } catch (Exception e) {
            log.error("发送VC仓库暂停错误钉钉通知失败", e);
        }
    }
}
