package com.suncent.smc.quartz.task.listing.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.service.IListingLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 标签同步服务
 * 提供统一的标签同步处理逻辑
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class LabelSyncService {

    @Autowired
    private LabelProcessorFactory processorFactory;

    @Autowired
    private IListingLabelService listingLabelService;

    /**
     * 同步指定店铺的所有标签类型
     *
     * @param shopCode 店铺编码
     */
    public void syncAllLabels(String shopCode) {
        log.info("开始同步店铺 {} 的所有标签", shopCode);
        long startTime = System.currentTimeMillis();

        List<LabelProcessor> processors = processorFactory.getAllProcessors();
        Map<String, Long> processingTimes = new HashMap<>();

        for (LabelProcessor processor : processors) {
            try {
                long processorStartTime = System.currentTimeMillis();
                syncLabels(shopCode, processor.getLabelType());
                long processorEndTime = System.currentTimeMillis();

                long processingTime = processorEndTime - processorStartTime;
                processingTimes.put(processor.getLabelType(), processingTime);

                log.info("店铺 {} 的 {} 标签同步完成，耗时: {}ms",
                        shopCode, processor.getLabelType(), processingTime);

            } catch (Exception e) {
                log.error("店铺 {} 的 {} 标签同步失败", shopCode, processor.getLabelType(), e);
            }
        }

        long totalTime = System.currentTimeMillis() - startTime;
        log.info("店铺 {} 的所有标签同步完成，总耗时: {}ms，详细耗时: {}",
                shopCode, totalTime, processingTimes);
    }

    /**
     * 同步指定类型的标签
     *
     * @param shopCode  店铺编码
     * @param labelType 标签类型
     */
    public void syncLabels(String shopCode, String labelType) {
        if (StrUtil.isBlank(shopCode) || StrUtil.isBlank(labelType)) {
            log.warn("店铺编码或标签类型为空，跳过同步: shopCode={}, labelType={}", shopCode, labelType);
            return;
        }

        LabelProcessor processor = processorFactory.getProcessor(labelType);

        // 简化为统一的分批处理方式
        syncLabelsBatched(shopCode, processor);
    }

    /**
     * 分批同步标签（改进版本 - 真正的分批删除重建）
     *
     * @param shopCode  店铺编码
     * @param processor 标签处理器
     */
    private void syncLabelsBatched(String shopCode, LabelProcessor processor) {
        log.info("开始分批同步店铺 {} 的 {} 标签", shopCode, processor.getLabelType());

        int offset = 0;
        int batchSize = processor.getBatchSize();
        int totalProcessed = 0;

        try {
            // 分批查询并立即处理每批数据
            while (true) {
                List<? extends Object> sourceData = processor.querySourceData(shopCode, offset, batchSize);

                if (CollUtil.isEmpty(sourceData)) {
                    log.debug("店铺 {} 的 {} 标签数据查询完成，offset: {}",
                            shopCode, processor.getLabelType(), offset);
                    break;
                }

                // 处理为标签
                List<ListingLabel> labels = processor.processToLabels(sourceData, shopCode);

                if (CollUtil.isNotEmpty(labels)) {
                    // 立即处理这一批：删除对应的旧标签，插入新标签
                    processBatchLabels(labels, processor, shopCode);
                    totalProcessed += labels.size();

                    log.debug("店铺 {} 的 {} 标签批次处理完成，本批次: {}条，累计: {}条",
                            shopCode, processor.getLabelType(), labels.size(), totalProcessed);
                }

                offset += batchSize;
            }

            log.info("店铺 {} 的 {} 标签分批同步完成，共处理: {}条",
                    shopCode, processor.getLabelType(), totalProcessed);

        } catch (Exception e) {
            log.error("店铺 {} 的 {} 标签分批同步失败", shopCode, processor.getLabelType(), e);
            throw e;
        }
    }

    /**
     * 处理单批标签：删除对应的旧标签，插入新标签
     *
     * @param labels    标签列表
     * @param processor 标签处理器
     * @param shopCode  店铺编码
     */
    private void processBatchLabels(List<ListingLabel> labels, LabelProcessor processor, String shopCode) {
        if (CollUtil.isEmpty(labels)) {
            return;
        }

        String labelType = processor.getLabelType();
        Set<String> deleteConditions = processor.getDeleteConditions();

        try {
            // 提取这批标签涉及的headId
            List<Integer> headIds = labels.stream()
                    .map(ListingLabel::getHeadId)
                    .distinct()
                    .collect(Collectors.toList());

            // 删除这些headId对应的旧标签
            if (CollUtil.isNotEmpty(headIds)) {
                int deletedCount;
                if (CollUtil.isNotEmpty(deleteConditions)) {
                    // 如果有特定的删除条件，按条件删除
                    boolean isExcludeMode = processor.isExcludeMode();
                    deletedCount = deleteByConditions(headIds, labelType, deleteConditions, shopCode, isExcludeMode);
                } else {
                    // 否则删除该类型的所有标签
                    deletedCount = listingLabelService.deleteListingLabelsByHeadIdsAndLabelType(headIds, labelType, shopCode);
                }
                log.debug("删除旧标签: {}条，涉及headId: {}个", deletedCount, headIds.size());
            }

            // 插入新标签
            Lists.partition(labels, 1000).forEach(listingLabelService::insertListingLabelBatch);

        } catch (Exception e) {
            log.error("处理批次标签失败，标签数量: {}, 标签类型: {}, 店铺: {}",
                    labels.size(), labelType, shopCode, e);
            throw e;
        }
    }

    /**
     * 根据删除条件删除标签
     *
     * @param headIds          头表ID列表
     * @param labelType        标签类型
     * @param deleteConditions 删除条件（标签值）
     * @param shopCode         店铺编码
     * @param isExcludeMode    是否为排除模式
     * @return 删除的标签数量
     */
    private int deleteByConditions(List<Integer> headIds, String labelType, Set<String> deleteConditions, String shopCode, boolean isExcludeMode) {
        if (isExcludeMode) {
            // 排除模式：删除该类型下不在条件列表中的标签
            return deleteListingLabelsByHeadIdsAndLabelTypeExcluding(headIds, labelType, deleteConditions, shopCode);
        } else {
            // 包含模式：只删除条件列表中的标签
            int totalDeleted = 0;
            for (String condition : deleteConditions) {
                int deleted = deleteListingLabelsByHeadIdsAndLabelTypeAndLabel(headIds, labelType, condition, shopCode);
                totalDeleted += deleted;
                log.debug("删除标签条件 '{}': {}条", condition, deleted);
            }
            return totalDeleted;
        }
    }

    /**
     * 根据headIds、标签类型和标签值删除标签
     * 这是一个临时实现，实际应该在ListingLabelService中添加对应方法
     */
    private int deleteListingLabelsByHeadIdsAndLabelTypeAndLabel(List<Integer> headIds, String labelType, String label, String shopCode) {
        // 临时实现：逐个删除
        int totalDeleted = 0;
        for (Integer headId : headIds) {
            try {
                // 检查是否存在该标签
                int count = listingLabelService.selectCountByHeadIdAndLabel(headId, label);
                if (count > 0) {
                    listingLabelService.deleteListingLabelByHeadIdAndLabel(headId, label);
                    totalDeleted++;
                }
            } catch (Exception e) {
                log.warn("删除标签失败: headId={}, label={}", headId, label, e);
            }
        }
        return totalDeleted;
    }

    /**
     * 根据headIds、标签类型删除标签，排除指定的标签值
     * 实现 NOT IN 逻辑
     */
    private int deleteListingLabelsByHeadIdsAndLabelTypeExcluding(List<Integer> headIds, String labelType, Set<String> excludeLabels, String shopCode) {
        // 这里需要在ListingLabelService中添加对应的方法
        // 临时实现：先查询所有该类型的标签，然后逐个删除不在排除列表中的标签
        int totalDeleted = 0;

        try {
            // 调用新的服务方法来批量删除
            totalDeleted = listingLabelService.deleteListingLabelsByHeadIdsAndLabelTypeExcluding(headIds, labelType, excludeLabels, shopCode);
            log.debug("排除模式删除标签: {}条，排除条件: {}", totalDeleted, excludeLabels);
        } catch (Exception e) {
            log.error("排除模式删除标签失败: headIds={}, labelType={}, excludeLabels={}",
                    headIds.size(), labelType, excludeLabels, e);
        }

        return totalDeleted;
    }


}
