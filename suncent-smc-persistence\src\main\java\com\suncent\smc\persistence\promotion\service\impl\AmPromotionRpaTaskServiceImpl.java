package com.suncent.smc.persistence.promotion.service.impl;

import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask;
import com.suncent.smc.persistence.promotion.mapper.AmPromotionRpaTaskMapper;
import com.suncent.smc.persistence.promotion.service.IAmPromotionRpaTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 促销RPA任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class AmPromotionRpaTaskServiceImpl implements IAmPromotionRpaTaskService {
    @Autowired
    private AmPromotionRpaTaskMapper amPromotionRpaTaskMapper;

    /**
     * 查询促销RPA任务
     *
     * @param id 促销RPA任务主键
     * @return 促销RPA任务
     */
    @Override
    public AmPromotionRpaTask selectAmPromotionRpaTaskById(Long id) {
        return amPromotionRpaTaskMapper.selectAmPromotionRpaTaskById(id);
    }

    /**
     * 查询促销RPA任务列表
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 促销RPA任务
     */
    @Override
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskList(AmPromotionRpaTask amPromotionRpaTask) {
        return amPromotionRpaTaskMapper.selectAmPromotionRpaTaskList(amPromotionRpaTask);
    }

    /**
     * 新增促销RPA任务
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 结果
     */
    @Override
    public int insertAmPromotionRpaTask(AmPromotionRpaTask amPromotionRpaTask) {
        amPromotionRpaTask.setCreateTime(DateUtils.getNowDate());
        return amPromotionRpaTaskMapper.insertAmPromotionRpaTask(amPromotionRpaTask);
    }

    /**
     * 修改促销RPA任务
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 结果
     */
    @Override
    public int updateAmPromotionRpaTask(AmPromotionRpaTask amPromotionRpaTask) {
        amPromotionRpaTask.setUpdateTime(DateUtils.getNowDate());
        return amPromotionRpaTaskMapper.updateAmPromotionRpaTask(amPromotionRpaTask);
    }

    /**
     * 批量删除促销RPA任务
     *
     * @param ids 需要删除的促销RPA任务主键
     * @return 结果
     */
    @Override
    public int deleteAmPromotionRpaTaskByIds(String ids) {
        return amPromotionRpaTaskMapper.deleteAmPromotionRpaTaskByIds(StringUtils.split(ids, ","));
    }

    /**
     * 删除促销RPA任务信息
     *
     * @param id 促销RPA任务主键
     * @return 结果
     */
    @Override
    public int deleteAmPromotionRpaTaskById(Long id) {
        return amPromotionRpaTaskMapper.deleteAmPromotionRpaTaskById(id);
    }

    /**
     * 根据BD记录ID查询RPA任务列表
     *
     * @param refBestDealId BD记录ID
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByRefId(Long refBestDealId) {
        return amPromotionRpaTaskMapper.selectAmPromotionRpaTaskByRefId(refBestDealId);
    }

    /**
     * 根据促销ID查询RPA任务列表
     *
     * @param promotionId 促销ID
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByPromotionId(String promotionId) {
        return amPromotionRpaTaskMapper.selectAmPromotionRpaTaskByPromotionId(promotionId);
    }

    /**
     * 查询待处理的RPA任务
     *
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectPendingAmPromotionRpaTasks() {
        return amPromotionRpaTaskMapper.selectPendingAmPromotionRpaTasks();
    }

    /**
     * 查询处理中的RPA任务
     *
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectProcessingAmPromotionRpaTasks() {
        return amPromotionRpaTaskMapper.selectProcessingAmPromotionRpaTasks();
    }

    /**
     * 查询已完成的RPA任务（成功或失败）
     *
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectCompletedAmPromotionRpaTasks() {
        return amPromotionRpaTaskMapper.selectCompletedAmPromotionRpaTasks();
    }

    /**
     * 根据状态查询RPA任务
     *
     * @param status 状态
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByStatus(Integer status) {
        return amPromotionRpaTaskMapper.selectAmPromotionRpaTaskByStatus(status);
    }

    /**
     * 根据活动类型查询RPA任务
     *
     * @param activityType 活动类型
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByActivityType(Integer activityType) {
        return amPromotionRpaTaskMapper.selectAmPromotionRpaTaskByActivityType(activityType);
    }

    /**
     * 根据操作类型查询RPA任务
     *
     * @param operationType 操作类型
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByOperationType(String operationType) {
        return amPromotionRpaTaskMapper.selectAmPromotionRpaTaskByOperationType(operationType);
    }

    /**
     * 查询需要SMC处理的任务
     *
     * @return 促销RPA任务集合
     */
    @Override
    public List<AmPromotionRpaTask> selectTasksNeedSmcHandle() {
        return amPromotionRpaTaskMapper.selectTasksNeedSmcHandle();
    }

    /**
     * 批量更新SMC处理标识
     *
     * @param ids        任务ID集合
     * @param handleFlag 处理标识
     * @return 结果
     */
    @Override
    public int batchUpdateHandleFlag(List<Long> ids, Integer handleFlag) {
        return amPromotionRpaTaskMapper.batchUpdateHandleFlag(ids, handleFlag);
    }

    /**
     * 创建促销RPA任务
     *
     * @param refBestDealId BD记录ID
     * @param operationType 操作类型
     * @param promotionId   促销ID
     * @param publishType   刊登类型
     * @param site          站点
     * @param activityType  活动类型
     * @param executeJson   执行JSON
     * @param createBy      创建人
     * @return 结果
     */
    @Override
    public int createPromotionRpaTask(Long refBestDealId, String operationType, String promotionId,
                                      Integer publishType, String site, Integer activityType,
                                      String executeJson, String createBy) {
        AmPromotionRpaTask task = new AmPromotionRpaTask();
        task.setRefBestDealId(refBestDealId);
        task.setOperationType(operationType);
        task.setPromotionId(promotionId);
        task.setPublishType(publishType);
        task.setSite(site);
        task.setActivityType(activityType);
        task.setExecuteJson(executeJson);
        task.setStatus(AmPromotionRpaTask.Status.PENDING.getCode());
        task.setHandleFlag(0);
        task.setDelFlag(0);
        task.setCreateBy(createBy);
        task.setCreateTime(DateUtils.getNowDate());

        return amPromotionRpaTaskMapper.insertAmPromotionRpaTask(task);
    }

    /**
     * 更新任务状态
     *
     * @param id       任务ID
     * @param status   状态
     * @param errorMsg 错误消息（可选）
     * @param updateBy 更新人
     * @return 结果
     */
    @Override
    public int updateTaskStatus(Long id, Integer status, String errorMsg, String updateBy) {
        AmPromotionRpaTask task = new AmPromotionRpaTask();
        task.setId(id);
        task.setStatus(status);
        task.setErrorMsg(errorMsg);
        task.setUpdateBy(updateBy);
        task.setUpdateTime(DateUtils.getNowDate());

        return amPromotionRpaTaskMapper.updateAmPromotionRpaTask(task);
    }

    /**
     * 标记任务为SMC已处理
     *
     * @param id       任务ID
     * @param updateBy 更新人
     * @return 结果
     */
    @Override
    public int markTaskAsHandled(Long id, String updateBy) {
        AmPromotionRpaTask task = new AmPromotionRpaTask();
        task.setId(id);
        task.setHandleFlag(1);
        task.setUpdateBy(updateBy);
        task.setUpdateTime(DateUtils.getNowDate());

        return amPromotionRpaTaskMapper.updateAmPromotionRpaTask(task);
    }
}
